package context

import (
	"encoding/json"
	"regexp"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	MAX_PARENT_THREAD = 10
)

func GetAWSOrgContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws org context", []string{resourceContext.TenantID})

	var (
		searchAfter       interface{}
		orgResourcesQuery = `{"_source":["entityId","tags","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AWS_ORG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		wg                sync.WaitGroup
		orgResourcesChan  = make(chan map[string]map[string]interface{}, MAX_PARENT_THREAD)
		semaphore         = make(chan struct{}, MAX_PARENT_THREAD)
	)

	go func() {
		for {
			orgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgResourcesQuery, searchAfter)
			if err != nil {
				close(orgResourcesChan)
				return
			}

			if len(orgResourcesDocs) > 0 {
				searchAfter = sortResponse
				orgResourcesChan <- orgResourcesDocs
			} else {
				close(orgResourcesChan)
				return
			}
		}
	}()

	for orgResourcesDocs := range orgResourcesChan {
		for orgID, orgResourcesDoc := range orgResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(doc map[string]interface{}) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processOrg(resourceContext, doc, orgID)
			}(orgResourcesDoc)
		}
	}

	wg.Wait()

	logger.Print(logger.INFO, "Processing complete for aws org context", []string{resourceContext.TenantID})
}

func processOrg(resourceContext *ResourceContext, orgResourcesDoc map[string]interface{}, orgResourcesDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
	)

	if orgID, ok := orgResourcesDoc["entityId"].(string); ok {

		contextDocID := common.GenerateCombinedHashID(orgID, common.AWS_ORG_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         orgID,
			ResourceType:       common.AWS_ORG_RESOURCE_TYPE,
			Account:            orgID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AWS_SERVICE_ID_INT,
			CloudResourceDocID: orgResourcesDocID,
		}

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, orgResourcesDoc, &resourceContextInsertDoc)

		if entityJSON, ok := orgResourcesDoc["entityJson"].(string); ok {

			entityJSONMap := make(map[string]interface{})

			if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}

			if orgEmail, ok := entityJSONMap["email"].(string); ok {
				resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
					resourceContext.GetUserContextItem(orgEmail, common.ORG_OWNER_USER_TYPE, "", "", nil),
				)
			}
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

}

func GetOrgUnitContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws org unit context", []string{resourceContext.TenantID})

	var (
		searchAfter           interface{}
		orgUnitResourcesQuery = `{"_source":["entityId","tags","accountId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"term":{"entityType.keyword":"` + common.AWS_ORGUNIT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		orgUnitResourcesChan  = make(chan map[string]map[string]interface{}, MAX_PARENT_THREAD)
		semaphore             = make(chan struct{}, MAX_PARENT_THREAD)
		wg                    sync.WaitGroup
		batchWg               sync.WaitGroup
		collectedDocIDs       []string
		mutex                 sync.Mutex
	)

	go func() {
		for {
			orgUnitResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgUnitResourcesQuery, searchAfter)
			if err != nil {
				close(orgUnitResourcesChan)
				return
			}

			if len(orgUnitResourcesDocs) > 0 {
				searchAfter = sortResponse
				orgUnitResourcesChan <- orgUnitResourcesDocs
			} else {
				close(orgUnitResourcesChan)
				return
			}
		}
	}()

	for orgUnitResourcesDocs := range orgUnitResourcesChan {
		for orgUnitDocID, orgUnitDoc := range orgUnitResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]interface{}) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processOrgUnitResource(resourceContext, docID, doc)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processResourceNamesForTeams(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(orgUnitDocID, orgUnitDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processResourceNamesForTeams(resourceContext, collectedDocIDs)
	}

	// Have to iterate again because parent of org unit can be another org unit
	searchAfter = nil
	orgUnitResourcesChan = make(chan map[string]map[string]interface{}, MAX_PARENT_THREAD)
	semaphore = make(chan struct{}, MAX_PARENT_THREAD)

	go func() {
		for {
			orgUnitResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgUnitResourcesQuery, searchAfter)
			if err != nil {
				close(orgUnitResourcesChan)
				return
			}

			if len(orgUnitResourcesDocs) > 0 {
				searchAfter = sortResponse
				orgUnitResourcesChan <- orgUnitResourcesDocs
			} else {
				close(orgUnitResourcesChan)
				return
			}
		}
	}()

	for orgUnitResourcesDocs := range orgUnitResourcesChan {
		for _, orgUnitResourcesDoc := range orgUnitResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(doc map[string]interface{}) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processOrgUnitForRelatedResource(resourceContext, doc)
			}(orgUnitResourcesDoc)
		}
	}

	wg.Wait()

	logger.Print(logger.INFO, "Processing complete for aws org unit context", []string{resourceContext.TenantID})
}

func processOrgUnitResource(resourceContext *ResourceContext, orgUnitResourcesDocID string, orgUnitResourcesDoc map[string]interface{}) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		orgUnitName              string
	)

	if orgUnitID, ok := orgUnitResourcesDoc["entityId"].(string); ok {

		orgOrOrgUnitID, _ := orgUnitResourcesDoc["accountId"].(string)

		entityJSONString, _ := orgUnitResourcesDoc["entityJson"].(string)
		entityJSON := make(map[string]interface{})
		if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err == nil {
			orgUnitName = getNameForAWSResource(orgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, "", entityJSONString)
		}

		contextDocID = common.GenerateCombinedHashID(orgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         orgUnitID,
			ResourceName:       orgUnitName,
			ResourceType:       common.AWS_ORGUNIT_RESOURCE_TYPE,
			Account:            orgOrOrgUnitID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AWS_SERVICE_ID_INT,
			CloudResourceDocID: orgUnitResourcesDocID,
		}

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getResourceInbuiltProperty(resourceContext, entityJSON, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, orgUnitResourcesDoc, &resourceContextInsertDoc)

		//TODO: Get owner from updated by in entity json

		if envName := GetEnvironmentNameFromValue(orgUnitName); len(envName) > 0 {
			resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
				common.ResourceContextItem{
					Name: envName,
					Type: common.ORGUNIT_NAME_ENV_TYPE,
				},
			)

			incrementParentChildEnvCount(resourceContext, envName, orgOrOrgUnitID, "")
		}

		if team := GetTeamNameFromValue(orgUnitName); len(team) > 0 {
			resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
				common.ResourceContextItem{
					Name: team,
					Type: common.ORGUNIT_NAME_TEAM_TYPE,
				},
			)
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

	return
}

func processOrgUnitForRelatedResource(resourceContext *ResourceContext, orgUnitDoc map[string]interface{}) {

	if orgUnitID, ok := orgUnitDoc["entityId"].(string); ok {

		contextDocID := common.GenerateCombinedHashID(orgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		orgOrOrgUnitID, _ := orgUnitDoc["accountId"].(string)

		if len(orgOrOrgUnitID) > 0 {

			orgUnitContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgUnitContextID); ok {

				if tmp, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
					tmp.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(orgRscCtxInsertDoc, common.AWS_ORGUNIT_RESOURCE_TYPE)
					tmp.ResourceEnvTypes.InheritedEnv = getInheritedEnv(orgRscCtxInsertDoc)
					tmp.ResourceTeamTypes.InheritedTeam = getInheritedTeam(orgRscCtxInsertDoc)
					resourceContext.SetResourceContextInsertDoc(contextDocID, tmp)

					assignRelatedResource(resourceContext, tmp.ResourceID, contextDocID, tmp.ResourceType,
						orgOrOrgUnitID, orgUnitContextID, common.AWS_ORGUNIT_RESOURCE_TYPE, false)
				}

			} else {

				orgContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORG_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgContextID); ok {

					if tmp, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
						tmp.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(orgRscCtxInsertDoc, common.AWS_ORG_RESOURCE_TYPE)
						tmp.ResourceEnvTypes.InheritedEnv = getInheritedEnv(orgRscCtxInsertDoc)
						resourceContext.SetResourceContextInsertDoc(contextDocID, tmp)

						assignRelatedResource(resourceContext, tmp.ResourceID, contextDocID, tmp.ResourceType,
							orgOrOrgUnitID, orgContextID, common.AWS_ORG_RESOURCE_TYPE, false)

					}
				}
			}
		}
	}

}

func GetAccountContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws account context", []string{resourceContext.TenantID})

	var (
		searchAfter           interface{}
		accountResourcesQuery = `{"_source":["entityId","entityJson","tags","accountId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AWS_ACCOUNT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		accountResourcesChan  = make(chan map[string]map[string]interface{}, MAX_PARENT_THREAD)
		semaphore             = make(chan struct{}, MAX_PARENT_THREAD)
		wg                    sync.WaitGroup
		batchWg               sync.WaitGroup
		collectedDocIDs       []string
		mutex                 sync.Mutex
	)

	go func() {
		for {
			accountResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, accountResourcesQuery, searchAfter)
			if err != nil {
				close(accountResourcesChan)
				return
			}

			if len(accountResourcesDocs) > 0 {
				searchAfter = sortResponse
				accountResourcesChan <- accountResourcesDocs
			} else {
				close(accountResourcesChan)
				return
			}
		}
	}()

	for accountResourcesDocs := range accountResourcesChan {
		for accountDocID, accountDoc := range accountResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]interface{}) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processAccount(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processResourceNamesForTeams(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(accountDocID, accountDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processResourceNamesForTeams(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for aws account context", []string{resourceContext.TenantID})
}

func processAccount(resourceContext *ResourceContext, accountResourcesDoc map[string]interface{}, accountResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		accountName              string
		entityJSONMap            = make(map[string]interface{})
	)

	if accountID, ok := accountResourcesDoc["entityId"].(string); ok {

		orgOrOrgUnitID, _ := accountResourcesDoc["accountId"].(string)

		if entityJSON, ok := accountResourcesDoc["entityJson"].(string); ok {
			accountName = getNameForAWSAccount(entityJSON)
			if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}
		}

		contextDocID = common.GenerateCombinedHashID(accountID, common.AWS_ACCOUNT_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         accountID,
			ResourceName:       accountName,
			ResourceType:       common.AWS_ACCOUNT_RESOURCE_TYPE,
			Account:            orgOrOrgUnitID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AWS_SERVICE_ID_INT,
			CloudResourceDocID: accountResourcesDocID,
		}

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getResourceInbuiltProperty(resourceContext, entityJSONMap, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, accountResourcesDoc, &resourceContextInsertDoc)

		// If org is not onboarded, accountId is self
		if orgOrOrgUnitID != accountID {

			// Account parent can be either org unit or org

			orgUnitContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORGUNIT_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if orgUnitRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgUnitContextID); ok {
				resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(orgUnitRscCtxInsertDoc, common.AWS_ORGUNIT_RESOURCE_TYPE)
				resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(orgUnitRscCtxInsertDoc)
				resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = getInheritedTeam(orgUnitRscCtxInsertDoc)

				assignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType,
					orgOrOrgUnitID, orgUnitContextID, common.AWS_ORGUNIT_RESOURCE_TYPE, false)
			} else {

				orgContextID := common.GenerateCombinedHashID(orgOrOrgUnitID, common.AWS_ORG_RESOURCE_TYPE, orgOrOrgUnitID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(orgRscCtxInsertDoc, common.AWS_ORG_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(orgRscCtxInsertDoc)

					assignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType,
						orgOrOrgUnitID, orgContextID, common.AWS_ORG_RESOURCE_TYPE, false)
				}
			}
		}

		if envName := GetEnvironmentNameFromValue(accountName); len(envName) > 0 {
			resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
				common.ResourceContextItem{
					Name: envName,
					Type: common.ACCOUNT_NAME_ENV_TYPE,
				},
			)

			incrementParentChildEnvCount(resourceContext, envName, orgOrOrgUnitID, "")
		}

		if team := GetTeamNameFromValue(accountName); len(team) > 0 {
			resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
				common.ResourceContextItem{
					Name: team,
					Type: common.ACCOUNT_NAME_TEAM_TYPE,
				},
			)
		}

		if accountEmail, ok := entityJSONMap["email"].(string); ok {
			resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
				resourceContext.GetUserContextItem(accountEmail, common.ACCOUNT_OWNER_USER_TYPE, "", "", nil),
			)
		}

		if contactInterfaceArray, ok := entityJSONMap["alternateContacts"].([]interface{}); ok {

			for _, contactInterface := range contactInterfaceArray {

				if contactMap, ok := contactInterface.(map[string]interface{}); ok {

					if ownerType, ok := contactMap["alternateContactType"].(string); ok {

						if username, ok := contactMap["emailAddress"].(string); ok {

							userType := common.ACCOUNT_CONTACT_USER_TYPE

							switch ownerType {

							case "BILLING":

								resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CostOwners,
									resourceContext.GetUserContextItem(username, userType, "", "", nil),
								)

							case "OPERATIONS":

								resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
									resourceContext.GetUserContextItem(username, userType, "", "", nil),
								)

							case "SECURITY":

								resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners,
									resourceContext.GetUserContextItem(username, userType, "", "", nil),
								)
							}
						}
					}
				}
			}
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

	}

	return
}

func GetAWSUserAndRoleContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws iam user and role context", []string{resourceContext.TenantID})

	var (
		searchAfter       interface{}
		iamResourcesQuery = `{"_source":["entityId","entityType","accountId","tags","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"entityType.keyword":["` + common.AWS_IAM_USER_RESOURCE_TYPE + `","` + common.AWS_IAM_ROLE_RESOURCE_TYPE + `"]}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}},"size":0,"aggs":{"entityId":{"terms":{"field":"entityId.keyword"}}}}`
		iamResourcesChan  = make(chan map[string]map[string]interface{}, MAX_PARENT_THREAD)
		semaphore         = make(chan struct{}, MAX_PARENT_THREAD)
		wg                sync.WaitGroup
		batchWg           sync.WaitGroup
		collectedDocIDs   []string
		mutex             sync.Mutex
	)

	go func() {
		for {
			iamResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, iamResourcesQuery, searchAfter)
			if err != nil {
				close(iamResourcesChan)
				return
			}

			if len(iamResourcesDocs) > 0 {
				searchAfter = sortResponse
				iamResourcesChan <- iamResourcesDocs
			} else {
				close(iamResourcesChan)
				return
			}
		}
	}()

	for iamResourcesDocs := range iamResourcesChan {
		for iamDocID, iamDoc := range iamResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]interface{}) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processIAMResource(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(iamDocID, iamDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for aws iam resource context", []string{resourceContext.TenantID})

}

type AWSIAMRoleEntity struct {
	TrustedEntities []struct {
		Type             string   `json:"type"`
		Identifier       string   `json:"identifier"`
		Actions          []string `json:"actions"`
		TrustedAccountID string   `json:"trustedAccountId"`
	} `json:"trustedEntities"`
}

func processIAMResource(resourceContext *ResourceContext, iamResourcesDoc map[string]interface{}, iamResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		uniqueIdentities         = make(map[string]struct{})
	)

	if iamID, ok := iamResourcesDoc["entityId"].(string); ok {
		if accountID, ok := iamResourcesDoc["accountId"].(string); ok {
			if entityType, ok := iamResourcesDoc["entityType"].(string); ok {
				contextDocID = common.GenerateCombinedHashID(iamID, entityType, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				resourceContextInsertDoc = common.ResourceContextInsertDoc{
					ResourceID:         iamID,
					ResourceName:       iamID,
					ResourceType:       entityType,
					Account:            accountID,
					TenantID:           resourceContext.TenantID,
					Region:             "Global",
					ServiceID:          common.AWS_SERVICE_ID_INT,
					CloudResourceDocID: iamResourcesDocID,
				}

				setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
				getTagContextOfResource(resourceContext, iamResourcesDoc, &resourceContextInsertDoc)

				if entityType == common.AWS_IAM_ROLE_RESOURCE_TYPE {
					if entityJSON, ok := iamResourcesDoc["entityJson"].(string); ok {
						var awsIAMRoleJSON AWSIAMRoleEntity
						if err := json.Unmarshal([]byte(entityJSON), &awsIAMRoleJSON); err != nil {
							logger.Print(logger.ERROR, "Failed to unmarshal", err)
							return
						}

						trustedEntities := awsIAMRoleJSON.TrustedEntities

						for _, trustedEntity := range trustedEntities {
							for identifierType, format := range map[string]string{
								"user":    `^arn:aws:iam::\d{12}:user/(.+)$`,
								"role":    `^arn:aws:iam::\d{12}:role/(.+)$`,
								"account": `^\d{12}$`} {

								r := regexp.MustCompile(format)
								if matches := r.FindStringSubmatch(trustedEntity.Identifier); len(matches) > 0 {

									switch identifierType {

									case "user":
										if len(matches) == 3 {
											userAccount := matches[1]
											user := matches[2]
											getAWSIAMEntityOwnersForPolicy(resourceContext, user, userAccount, common.AWS_IAM_USER_RESOURCE_TYPE, &resourceContextInsertDoc,
												uniqueIdentities, "User owned IAM User "+user+" has been added as a trusted entity for the resource", 1)
										}
									case "role":
										if len(matches) == 3 {
											roleAccount := matches[1]
											role := matches[2]
											getAWSIAMEntityOwnersForPolicy(resourceContext, role, roleAccount, common.AWS_IAM_ROLE_RESOURCE_TYPE, &resourceContextInsertDoc,
												uniqueIdentities, "User owned IAM Role "+role+" has been added as a trusted entity for the resource", 1)
										}
									case "account":

										accContextDocID := common.GenerateCombinedHashID(trustedEntity.Identifier, common.AWS_ACCOUNT_RESOURCE_TYPE, trustedEntity.Identifier, resourceContext.LastCollectedAt, resourceContext.TenantID)
										origin := "Internal"
										if _, ok := resourceContext.GetResourceContextInsertDoc(accContextDocID); !ok {
											origin = "External"
										}

										resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
											resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(trustedEntity.Identifier+ACCOUNT_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
												origin+" AWS account has been added as trusted entity for the resource", "", nil))
									}
								}
							}
						}
					}
				}

				accContextID := common.GenerateCombinedHashID(accountID, common.AWS_ACCOUNT_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if accRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(accContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(accRscCtxInsertDoc, common.AWS_ACCOUNT_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(accRscCtxInsertDoc)
					resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CostOwners, accRscCtxInsertDoc.CostOwners...)
					resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners, accRscCtxInsertDoc.SecurityOwners...)
					resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners, accRscCtxInsertDoc.OpsOwners...)
				}

				if envName := GetEnvironmentNameFromValue(iamID); len(envName) > 0 {
					resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: envName,
							Type: common.RESOURCE_NAME_ENV_TYPE,
						},
					)
				}

				resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

			}
		}
	}

	return
}

func GetSecurityGroupRules(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for aws security group rules", []string{resourceContext.TenantID})

	var (
		searchAfter      interface{}
		sgResourcesQuery = `{"_source":["entityId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AWS_SG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AWS_SERVICE_ID + `}}]}}}`
		sgResourcesChan  = make(chan map[string]map[string]interface{}, MAX_PARENT_THREAD)
		semaphore        = make(chan struct{}, MAX_PARENT_THREAD)
		wg               sync.WaitGroup
	)

	go func() {
		for {
			sgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, sgResourcesQuery, searchAfter)
			if err != nil {
				close(sgResourcesChan)
				return
			}

			if len(sgResourcesDocs) > 0 {
				searchAfter = sortResponse
				sgResourcesChan <- sgResourcesDocs
			} else {
				close(sgResourcesChan)
				return
			}
		}
	}()

	for sgResourcesDocs := range sgResourcesChan {
		for sgDocID, sgDoc := range sgResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]interface{}) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processSGResource(resourceContext, doc)
			}(sgDocID, sgDoc)
		}
	}

	wg.Wait()
	logger.Print(logger.INFO, "Processing complete for aws security group rules", []string{resourceContext.TenantID})
}

func processSGResource(resourceContext *ResourceContext, sgResourcesDoc map[string]interface{}) {
	var networkInboundPorts = make(map[int]struct{})

	if sgID, ok := sgResourcesDoc["entityId"].(string); ok {
		if entityJson, ok := sgResourcesDoc["entityJson"].(string); ok {
			entityJsonMap := make(map[string]interface{})

			if err := json.Unmarshal([]byte(entityJson), &entityJsonMap); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}

			if inboundRules, ok := entityJsonMap["inboundRules"].([]interface{}); ok {
				for _, inboundRule := range inboundRules {
					if inboundRuleJson, ok := inboundRule.(map[string]interface{}); ok {
						fromPort, _ := inboundRuleJson["fromPort"].(float64)
						toPort, _ := inboundRuleJson["toPort"].(float64)

						networkInboundPorts[int(fromPort)] = struct{}{}
						networkInboundPorts[int(toPort)] = struct{}{}
					}
				}
			}
		}

		resourceContext.SetNetworkInboundPorts(strings.ToLower(sgID), networkInboundPorts)
	}
}
