package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
)

func GetSoftwareNameFromValue(str string) string {

	str = strings.ToLower(str)

	for software, values := range softwareValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				return software
			}
		}
	}

	return ""
}

func GetSoftwareNameListFromValue(str string) []string {

	softwareNames := make([]string, 0)
	str = strings.ToLower(str)

	for software, values := range softwareValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				softwareNames = append(softwareNames, software)
			}
		}
	}

	return softwareNames
}

func GetUniqueSoftwareContext(resourceContextDoc *common.ResourceContextInsertDoc) (software []string) {

	uniqueSoftware := make(map[string]struct{})

	for _, v := range resourceContextDoc.ResourceSoftwareTypes.DefinedSoftware {
		uniqueSoftware[v.Name] = struct{}{}
	}
	for _, v := range resourceContextDoc.ResourceSoftwareTypes.DerivedSoftware {
		uniqueSoftware[v.Name] = struct{}{}
	}

	for softwareName, _ := range uniqueSoftware {
		software = append(software, softwareName)
	}

	return
}
