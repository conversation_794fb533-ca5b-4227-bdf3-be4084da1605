package context

import (
	"encoding/json"
	"slices"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func getOktaUsers(resourceContext *ResourceContext, userMaps *UserMaps) {

	var (
		searchAfter    interface{}
		oktaUsersQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"idpType.keyword":"` + common.OKTA_IDP_TYPE + `"}}]}}}`
	)

	for {
		oktaUsersDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDP_USERS_INDEX}, oktaUsersQuery, searchAfter)
		if err != nil {
			return
		}

		if len(oktaUsersDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, oktaUsersDoc := range oktaUsersDocs {

			oktaUser, err := json.Marshal(oktaUsersDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", []string{resourceContext.TenantID}, err)
				continue
			}

			var oktaUserDoc common.IDPUsersDoc

			if err = json.Unmarshal(oktaUser, &oktaUserDoc); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling document", []string{resourceContext.TenantID}, err)
				continue
			}

			if addr, err := common.ParseAddress(oktaUserDoc.Email); err == nil {

				// TODO: Check if this is a cloud user by verifying applications they have access to

				for _, app := range oktaUserDoc.Apps {
					resourceContext.SetIDPMappedUser(app.AppUsername, addr.Address)
				}

				var (
					userResource UserContext
					deleted      bool
					userID       = oktaUserDoc.UserID // default value email
				)

				if oktaUserDoc.Status == "DEPROVISIONED" || oktaUserDoc.Status == "DELETED" {
					deleted = true
				}

				if u, ok := resourceContext.GetUserResource(addr.Address); ok {
					if !u.Active && !deleted {
						// If an okta user existed before which was deleted and then got created again with same email
						// Consider the active one
						userResource = UserContext{
							ID:            addr.Address,
							Name:          common.ConvertToTitleCase(oktaUserDoc.Name),
							Email:         addr.Address,
							Type:          oktaUserDoc.UserType,
							CloudResource: true,
							Active:        !deleted,
						}
					} else {
						userResource = u
					}
				} else {
					userResource = UserContext{
						ID:            addr.Address,
						Name:          common.ConvertToTitleCase(oktaUserDoc.Name),
						Email:         addr.Address,
						Type:          oktaUserDoc.UserType,
						CloudResource: true,
						Active:        !deleted,
					}
				}

				if _, err := common.ParseAddress(userResource.Name); err == nil || len(userResource.Name) <= 0 {
					userResource.Name = common.GetFormattedNameFromEmail(addr.Address)
				}

				if userResource.Active {
					userMaps.emailNameMap[addr.Address] = userResource.Name
				} else {
					resourceContext.SetEmailStatus(addr.Address, false)
					resourceContext.SetUpdateUndeliverableValidEmail(addr.Address)
				}

				resourceContext.SetUserIDToEmail(userID, addr.Address)

				if len(userResource.Manager) <= 0 {
					userResource.Manager = oktaUserDoc.ManagerID
					if len(userResource.Manager) <= 0 {
						userResource.Manager = oktaUserDoc.ManagerName
					}
				}

				if len(userResource.Groups) <= 0 {
					userResource.Groups = make(map[string]struct{})
				}

				for _, group := range oktaUserDoc.Groups {
					userResource.Groups[group] = struct{}{}
				}

				if len(oktaUserDoc.Department) > 0 {
					if !slices.Contains(userResource.Team, FormatContextValue(oktaUserDoc.Department)) {
						userResource.Team = append(userResource.Team, FormatContextValue(oktaUserDoc.Department))
					}
				}

				if len(userResource.Department) <= 0 {
					userResource.Department = oktaUserDoc.Department
				}

				if len(userResource.JobTitle) <= 0 {
					userResource.JobTitle = oktaUserDoc.Title
				}

				if len(userResource.Department) > 0 || len(userResource.JobTitle) > 0 {
					orgMapKey := userResource.Department + userResource.JobTitle

					// Should we add okta organization as well?
					userMaps.orgMap[orgMapKey] = append(userMaps.orgMap[orgMapKey], addr.Address)
				}

				var isPrimaryDomain bool

				for _, primaryDomain := range resourceContext.PrimaryDomains {
					if strings.HasSuffix(strings.ToLower(addr.Address), strings.ToLower(primaryDomain)) {
						isPrimaryDomain = true
						break
					}
				}

				if isPrimaryDomain {
					// If partner email, prefer company domain email more
					resourceContext.SetUserResource(addr.Address, userResource)
				}
			}
		}
	}
}
