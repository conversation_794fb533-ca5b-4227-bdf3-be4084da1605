package context

import (
	"encoding/json"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type UserMaps struct {
	orgMap                 map[string][]string
	emailNameMap           map[string]string
	identitySDDLPrimaryMap map[string]string // store the mapping of parent and child identities for sddl identities
}

func GetUsers(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Gathering cloud users", []string{resourceContext.TenantID})

	var (
		userMaps = UserMaps{
			orgMap:                 make(map[string][]string),
			emailNameMap:           make(map[string]string),
			identitySDDLPrimaryMap: make(map[string]string),
		}
	)

	getCloudUsers(resourceContext, &userMaps)

	if enabled, ok := resourceContext.GetEnabledService("okta"); ok && enabled {
		getOktaUsers(resourceContext, &userMaps)
	}

	postProcessIdentities(resourceContext, &userMaps)

	logger.Print(logger.INFO, "Gathered cloud users", []string{resourceContext.TenantID})
}

func getCloudUsers(resourceContext *ResourceContext, userMaps *UserMaps) {

	var (
		searchAfter              interface{}
		cloudUserIdentitiesQuery = `{"_source":["identityId","identityStatus","name","type","deleted","additionalInfo","accessKeys","accountId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}}]}}}`
	)

	for {
		cloudUserIdentitiesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, cloudUserIdentitiesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(cloudUserIdentitiesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for identityDocID, cloudUserIdentitiesDoc := range cloudUserIdentitiesDocs {
			processIdentityDoc(identityDocID, resourceContext.TenantID, cloudUserIdentitiesDoc, resourceContext, userMaps)
		}
	}
}

func processIdentityDoc(identityDocID, tenantID string, cloudUserIdentitiesDoc map[string]interface{}, resourceContext *ResourceContext, userMaps *UserMaps) {

	var (
		identitiesDoc    common.IdentitiesDoc
		hasConsoleAccess = true
	)

	identitiesDocBytes, err := json.Marshal(cloudUserIdentitiesDoc)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling identities doc to JSON", []string{tenantID}, err)
		return
	}

	if err = json.Unmarshal(identitiesDocBytes, &identitiesDoc); err != nil {
		logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
		return
	}

	originalIdentityID := identitiesDoc.IdentityID
	identityID := identitiesDoc.IdentityID
	if len(identityID) <= 0 {
		return
	}
	identityID = strings.ToLower(identityID)

	identityType := identitiesDoc.Type
	if len(identityType) <= 0 {
		return
	}

	if isServiceIdentityType(identityType, identitiesDoc) {
		return
	}

	// precize created identities will already have non-human and email status evaluated
	if isPrecizeCreatedIdentity(identityType) {
		switch identitiesDoc.IdentityStatus {
		case common.CURRENT_EMPLOYEE_IDENTITY_STATUS:
			resourceContext.SetAliasUpdate(identityID, false)
		case common.EX_EMPLOYEE_IDENTITY_STATUS:
			resourceContext.SetAliasUpdate(identityID, false)
		case common.VALID_IDENTITY_STATUS:
			resourceContext.SetAliasUpdate(identityID, true)
		}
	}

	additionalInfo := make(map[string]interface{})
	if err := json.Unmarshal([]byte(identitiesDoc.AdditionalInfo), &additionalInfo); err != nil {
		return
	}

	switch identityType {

	case common.AWS_USER_IDENTITY_TYPE:

		// AWS IAM Users without console access are used for programmatic access
		// They should not be considered as a user, but as a service identitity only
		if iamUserHasConsoleAccess, ok := additionalInfo["hasLoginProfile"].(bool); ok && !iamUserHasConsoleAccess {
			hasConsoleAccess = false
		}

	case common.AWS_SSOUSER_IDENTITY_TYPE:

		if userDetails, ok := additionalInfo["user"].(map[string]interface{}); ok {
			if userName, ok := userDetails["userName"].(string); ok && len(userName) > 0 {
				if addr, err := common.ParseAddress(identityID); err == nil {
					resourceContext.SetIDPMappedUser(userName, addr.Address)
				}
			}
		}
	}

	// Apply typo exceptions
	if vals, ok := resourceContext.GetTypoExceptions(identityID); ok && len(vals) == 1 {
		identityID = vals[0]
	}

	identityName := common.ConvertToTitleCase(identitiesDoc.Name)

	if _, err := common.ParseAddress(identityName); err == nil {
		identityName = common.GetFormattedNameFromEmail(identityName)
	}

	extractIdentityNameFromAdditionalInfo(&identityName, identityType, additionalInfo)

	deleted, cloudUser := determineUserStatus(identitiesDoc)

	isSddl := handleIdentitiesWithSDDL(&identityID, &identityName, identityDocID, resourceContext, userMaps)

	addr, err := common.ParseAddress(identityID)
	if err != nil {
		email := deriveEmailFromName(identityName, resourceContext)
		if addr, err = common.ParseAddress(email); err != nil {
			return
		}

		// user with no email should be merged if derived email is valid
		// Iam users without console access should not be considered as a user and hence should not be merged
		if hasConsoleAccess {
			resourceContext.SetChildPrimaryEmail(originalIdentityID, email)
		} else {

			if len(email) > 0 {
				identityName = common.GetFormattedNameFromEmail(email)
			}
			resourceContext.SetChildPrimaryEmail(originalIdentityID, "")
		}
	}

	if addr, err := common.ParseAddress(identityName); err == nil {
		identityName = common.GetFormattedNameFromEmail(addr.Address)
	}

	userResource := UserContext{
		ID:            addr.Address,
		Name:          identityName,
		Email:         addr.Address,
		Type:          identityType,
		CloudResource: true,
		CloudUser:     cloudUser,
		Active:        !deleted,
		IsSddl:        isSddl,
		IdentityDocID: identityDocID,
	}

	if !hasConsoleAccess {
		userResource.SkipUser = true
	}

	updateEmailToNameMapping(resourceContext, addr.Address, identityName)

	addEmailToEmailStatusReq(resourceContext, userResource, userResource.Active, isSddl, userMaps)

	if identityType == common.AZURE_AD_USER_IDENTITY_TYPE {
		processAzureADUser(resourceContext, userMaps, identitiesDoc.AdditionalInfo, &userResource, addr.Address)
	} else {
		// Add user to UserResource if not present
		if existingADUsrRsc, ok := resourceContext.GetUserResource(addr.Address); !ok {
			resourceContext.SetUserResource(addr.Address, userResource)
		} else {
			// is sddl flag should be preserved in case of duplicate user resources
			if existingADUsrRsc.IsSddl && !userResource.IsSddl {
				userResource.IsSddl = true
			}

			// cloud user should be preserved in case of duplicate user resources
			if existingADUsrRsc.CloudUser && !userResource.CloudUser {
				userResource.CloudUser = true
			}
		}
	}
}

func postProcessIdentities(resourceContext *ResourceContext, userMaps *UserMaps) {

	batchSize := 10
	emailNameMap := make(map[string]string)
	for email, name := range userMaps.emailNameMap {
		emailNameMap[email] = name
		if len(emailNameMap) >= batchSize {
			err := ProcessBatchEmailValidity(emailNameMap, resourceContext)
			if err != nil {
				emailNameMap = make(map[string]string)
				continue
			}

			emailNameMap = make(map[string]string)
		}
	}

	if len(emailNameMap) > 0 {
		if err := ProcessBatchEmailValidity(emailNameMap, resourceContext); err != nil {
			//skip, if error is returned
		}
	}

	resourceContext.RangeUserResources(func(email string, userResource UserContext) bool {

		temp := userResource

		if fullName, ok := resourceContext.GetEmailToFullName(userResource.Email); ok {
			temp.Name = fullName
		}

		if isActive, ok := resourceContext.GetEmailStatus(temp.Email); ok {
			temp.Active = isActive
		}

		if len(temp.Team) <= 0 && len(temp.Manager) > 0 {
			if managerEmail, ok := resourceContext.GetUserIDToEmail(userResource.Manager); ok {
				if managerUser, ok := resourceContext.GetUserResource(managerEmail); ok {
					if len(managerUser.Team) > 0 {
						temp.Team = managerUser.Team
					}
				}
			}
		}

		if strings.Contains(strings.ToLower(temp.Email), EXT_KEYWORD) {
			handleIdentityWithExternalEmail(resourceContext, &temp)
		}

		if strings.Contains(strings.ToLower(temp.Email), LIVE_MICROSOFT_KEYWORD) || strings.Contains(strings.ToLower(temp.Email), MAIL_MICROSOFT_KEYWORD) {
			handlePersonalAndMicrosoftEmail(resourceContext, &temp)
		}

		HandleChildEmail(resourceContext, &temp)

		// Reassigned for ex employees
		if !temp.Active {
			handleExEmployeeReassignment(resourceContext, &temp, userMaps, email)
		}

		if ok := resourceContext.GetInvalidEmailCache(temp.Email); ok {
			temp.IsInvalid = true
		}
		temp.ID = temp.Email

		resourceContext.DeleteUserResource(email)
		resourceContext.SetUserResource(temp.Email, temp)
		return true
	})

}

func ProcessBatchEmailValidity(emailNameMap map[string]string, resourceContext *ResourceContext) error {

	emailStatusRes, err := checkEmailValidity(emailNameMap, false, resourceContext)
	if err != nil {
		return err
	}

	for email, status := range emailStatusRes {
		resourceContext.SetEmailStatus(email, status)
	}

	return nil
}
