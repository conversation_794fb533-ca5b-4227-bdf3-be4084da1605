package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
)

func GetAppNameFromValue(str string) (app string) {

	var (
		matched    string
		matchedLen int
	)

	str = strings.ToLower(str)

	for appName, values := range appValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) && len(appName) > matchedLen {
				matchedLen = len(appName)
				matched = appName
			}
		}
	}

	if len(matched) > 0 {
		app = matched
	}

	return
}

func GetAppNameListFromValue(str string) []string {

	appNames := make([]string, 0)
	str = strings.ToLower(str)

	for app, values := range appValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				appNames = append(appNames, app)
			}
		}
	}

	return appNames
}

func GetUniqueAppContext(resourceContextDoc *common.ResourceContextInsertDoc) (app []string) {

	uniqueApp := make(map[string]struct{})

	for _, v := range resourceContextDoc.ResourceAppTypes.DefinedApp {
		uniqueApp[v.Name] = struct{}{}
	}
	for _, v := range resourceContextDoc.ResourceAppTypes.DerivedApp {
		uniqueApp[v.Name] = struct{}{}
	}

	for appName, _ := range uniqueApp {
		app = append(app, appName)
	}

	return
}

func AddApplicationToGlobalApps(application string) {

	globalValuesMutex.Lock()

	var globallyExists bool

	for _, defaultAppValue := range defaultAppValues {
		subApps := strings.Split(defaultAppValue, ",")
		for _, subApp := range subApps {
			if strings.ToLower(subApp) == strings.ToLower(application) {
				globallyExists = true
				break
			}
		}

		if globallyExists {
			break
		}
	}

	if !globallyExists {
		defaultAppValues = append(defaultAppValues, application)
	}

	globalValuesMutex.Unlock()
}
