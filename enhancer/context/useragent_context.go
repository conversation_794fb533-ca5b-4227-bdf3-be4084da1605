package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
)

func GetUserAgentCategoriesFromValue(userAgent string) (categories []string) {
	userAgent = strings.ToLower(userAgent)

	var lowPriorityUserAgents = map[string]bool{
		AWS_SERVICE_USER_AGENT: true,
	}

	matches := make(map[string]bool)
	for category, patterns := range userAgentKeyOrValues {
		for _, pattern := range patterns {
			regex := regexp.MustCompile(pattern)
			if regex.MatchString(userAgent) {
				matches[category] = true
				break
			}
		}
	}

	if len(matches) > 1 {
		for category := range matches {
			if !lowPriorityUserAgents[category] {
				categories = append(categories, category)
			}
		}
		if len(categories) > 0 {
			return categories
		}
	}

	for category := range matches {
		categories = append(categories, category)
	}

	return categories
}

func GetUniqueUserAgentContext(resourceContextDoc *common.ResourceContextInsertDoc) (agents []string) {

	uniqueUsrAgent := make(map[string]struct{})

	for _, v := range resourceContextDoc.UserAgents {
		uniqueUsrAgent[v] = struct{}{}
	}

	for usrAgent, _ := range uniqueUsrAgent {
		agents = append(agents, usrAgent)
	}

	return
}
