package incidents

import (
	"fmt"
	"math"
	"strings"

	"github.com/precize/common"
)

const (
	OPENTOINTERNET_CRITERIA               = "is externally exposed"
	NOTOPENTOINTERNET_CRITERIA            = "is not externally exposed"
	SENSITIVEDATA_OPENTOINTERNET_CRITERIA = "has personal or other sensitive data which is exposed"
	SENSITIVEDATA_CRITERIA                = "has personal or other sensitive data"
	PRODENV_CRITERIA                      = "belongs to production environment"
	DEVENV_CRITERIA                       = "belongs to development environment"
	QAENV_CRITERIA                        = "belongs to staging environment"
	SANDBOXENV_CRITERIA                   = "belongs to sandbox environment"
	QAPRODACCENV_CRITERIA                 = "has many production resources"
	QANONPRODACCENV_CRITERIA              = "has negligible production resources"
	OWNERABSENT_CRITERIA                  = "has no owner identified"
	EXEMPLOYEE_CRITERIA                   = "is owned by an ex employee"
	FEWNOOWNERACC_CRITERIA                = "has an owner for most resources"
	NOOWNERACC_CRITERIA                   = "has no owner for most resources"
	MODERATE_NOOWNERACC_CRITERIA          = "has no owner for some resources"
	ACTIVEACC_CRITERIA                    = "is very active"
	MODERATELYACTIVEACC_CRITERIA          = "has moderate activity"
	INACTIVEACC_CRITERIA                  = "is not very active"
	IMPACTFULRESOURCEACCOUNT_CRITERIA     = "contains many significant resources"
	FEWIMPACTFULRESOURCEACCOUNT_CRITERIA  = "contains some significant resources"
	NOIMPACTFULRESOURCEACCOUNT_CRITERIA   = "contains very few significant resources"
	ACCOUNTSIZE_CRITERIA                  = "has a major share of organization resources"
	FEWACCOUNTSIZE_CRITERIA               = "has a minor share of organization resources"
	MODERATEACCOUNTSIZE_CRITERIA          = "has a moderate share of organization resources"
	EXEMPACC_CRITERIA                     = "has many ex-employee owned resources"
	FEWEXEMPACC_CRITERIA                  = "has less ex-employee owned resources"
	UNIQUEOWNERS_CRITERIA                 = "is used by a wide range of users"
	MODERATEUNIQUEOWNERS_CRITERIA         = "is used by a moderate number of users"
	FEWUNIQUEOWNERS_CRITERIA              = "is used by few users"
	VERYFEWUNIQUEOWNERS_CRITERIA          = "is used by very few users"
	NOCHANGE_CRITERIA                     = "No change in incident severity"
	PRECIZERISK_CRITERIA                  = "Precize evaluated this risk"
	IMPACTFULRESOURCE_CRITERIA            = "is significant, because it exists in a production environment with high activity and is being used by a wide range of users"
	MODERATEIMPACTFULRESOURCE_CRITERIA    = "is significant, because it exists in a production environment with moderate activity and user engagement"
	NOTIMPACTFULRESOURCE_CRITERIA         = "is not a significant resource"
	RSC_VULN_NOTOPENTOINTERNET            = "has vulnerabilities but is not externally exposed"
	RSC_VULN_OPENTOINTERNET               = "has vulnerabilities and is externally exposed"
	CRITICAL_VULNERABILITIES              = "has critical vulnerabilities"
	NO_CRITICAL_VULNERABILITIES           = "has no critical vulnerabilities"
	NO_SEVERE_VULNERABILITIES             = "does not have many severe vulnerabilities"
	OPENTOINTERNET_NOT_ENCRYPTED_CRITERIA = "is externally exposed and data is not encrypted"
	PRECIZE_HERO_STATS_CRITERIA           = "has some other configuration issues identified by precize"

	VERY_HIGH_ACCOUNT_COST_CRITERIA = "has significant monthly cost relative to other accounts"
	HIGH_COST_ACCOUNT_CRITERIA      = "has considerable monthly cost compared to other accounts"
	LOW_COST_ACCOUNT_CRITERIA       = "has relatively lower monthly costs compared to other accounts"
	HAS_COSTCENTER_CRITERIA         = "has assigned a cost center for most resources for better cost tracking"
	NO_COSTCENTER_CRITERIA          = "lacks proper cost center assignment for cost tracking"

	EXUSER_IDENTITY_CRITERIA          = "identity is an ex-employee"
	CURRENTEMP_IDENTITY_CRITERIA      = "identity is current employee"
	INACTIVE_IDENTITY_CRITERIA        = "identity is not an active cloud user"
	NOACTIVITY_IDENTITY_CRITERIA      = "identity has performed no activity"
	ACTIVITY_PERMHS_IDENTITY_CRITERIA = "identity has performed activities and is linked to permission related hero stats"

	SIG_EXT_INCIDENTS_CRITERIA     = "has significant configuration issues identified by external integrations"
	SOME_EXT_INCIDENTS_CRITERIA    = "has some configuration issues identified by external integrations"
	MINOR_EXT_INCIDENTS_CRITERIA   = "has minor configuration issues identified by external integrations"
	VERYFEW_EXT_INCIDENTS_CRITERIA = "has very few configuration issues identified by external integrations"

	VULNERABILITY_TYPE = "Vulnerability"
	MALWARE_TYPE       = "Malware"

	LOGGING            = "logging"
	MONITORING         = "monitoring"
	DEBUGGING          = "debugging"
	ENABLE_IF_REQUIRED = "enable if required"
	AUDIT              = "audit"
	ENABLE             = "enable"
	ENCRYPTION         = "encryption"
	CMDCENTER_LOG      = "_log_"

	VULNERABILITY_CATEGORY        = "Vulnerability"
	MALWARE_CATEGORY              = "Malware"
	GOVERNANCE_CATEGORY           = "Governance"
	SENSITIVEDATA_CATEGORY        = "SensitiveData"
	OPENTOINTERNET_CATEGORY       = "OpenToInternet"
	SENSITIVEDATAEXPOSED_CATEGORY = "SensitiveDataExposed"
)

type CriteriaInfo struct {
	Type     string
	Severity bool
}

// Severity true -> resource level criteria
// Severity false -> account level criteria
var criteriaMap = map[string]CriteriaInfo{
	OPENTOINTERNET_CRITERIA: {
		Type:     "likelihood",
		Severity: true,
	},
	NOTOPENTOINTERNET_CRITERIA: {
		Type:     "likelihood",
		Severity: true,
	},
	PRODENV_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	DEVENV_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	QAENV_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	QAPRODACCENV_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	QANONPRODACCENV_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	OWNERABSENT_CRITERIA: {
		Type:     "likelihood",
		Severity: true,
	},
	EXEMPLOYEE_CRITERIA: {
		Type:     "likelihood",
		Severity: true,
	},
	FEWNOOWNERACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	NOOWNERACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	MODERATE_NOOWNERACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	ACTIVEACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	MODERATELYACTIVEACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	INACTIVEACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	IMPACTFULRESOURCEACCOUNT_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	FEWIMPACTFULRESOURCEACCOUNT_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	NOIMPACTFULRESOURCEACCOUNT_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	ACCOUNTSIZE_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	FEWACCOUNTSIZE_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	MODERATEACCOUNTSIZE_CRITERIA: {
		Type:     "impact",
		Severity: false,
	},
	EXEMPACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	FEWEXEMPACC_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	UNIQUEOWNERS_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	MODERATEUNIQUEOWNERS_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	FEWUNIQUEOWNERS_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	VERYFEWUNIQUEOWNERS_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	NOCHANGE_CRITERIA: {
		Type:     "likelihood",
		Severity: false,
	},
	IMPACTFULRESOURCE_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	MODERATEIMPACTFULRESOURCE_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	NOTIMPACTFULRESOURCE_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	RSC_VULN_NOTOPENTOINTERNET: {
		Type:     "likelihood",
		Severity: true,
	},
	RSC_VULN_OPENTOINTERNET: {
		Type:     "likelihood",
		Severity: true,
	},
	CRITICAL_VULNERABILITIES: {
		Type:     "risk",
		Severity: true,
	},
	NO_CRITICAL_VULNERABILITIES: {
		Type:     "risk",
		Severity: false,
	},
	NO_SEVERE_VULNERABILITIES: {
		Type:     "risk",
		Severity: true,
	},
	SENSITIVEDATA_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	OPENTOINTERNET_NOT_ENCRYPTED_CRITERIA: {
		Type:     "impact",
		Severity: true,
	},
	PRECIZE_HERO_STATS_CRITERIA: {
		Type:     "likelihood",
		Severity: true,
	},
}

var likelihoodStringToNumber = map[string]float32{
	common.VERY_UNLIKELY_LIKELIHOOD: 0.0,
	common.UNLIKELY_LIKELIHOOD:      1.0,
	common.POSSIBLE_LIKELIHOOD:      2.0,
	common.LIKELY_LIKELIHOOD:        3.0,
	common.VERY_LIKELY_LIKELIHOOD:   4.0,
}

var likelihoodNumberToString = map[float32]string{
	0.0: common.VERY_UNLIKELY_LIKELIHOOD,
	1.0: common.UNLIKELY_LIKELIHOOD,
	2.0: common.POSSIBLE_LIKELIHOOD,
	3.0: common.LIKELY_LIKELIHOOD,
	4.0: common.VERY_LIKELY_LIKELIHOOD,
}

var impactStringToNumber = map[string]float32{
	common.NEGLIGIBLE_IMPACT:  0.0,
	common.MINOR_IMPACT:       1.0,
	common.MODERATE_IMPACT:    2.0,
	common.SIGNIFICANT_IMPACT: 3.0,
	common.SEVERE_IMPACT:      4.0,
}

var impactNumberToString = map[float32]string{
	0.0: common.NEGLIGIBLE_IMPACT,
	1.0: common.MINOR_IMPACT,
	2.0: common.MODERATE_IMPACT,
	3.0: common.SIGNIFICANT_IMPACT,
	4.0: common.SEVERE_IMPACT,
}

var riskScoreMap = map[string]float32{
	common.LOW_RISK:      2.0,
	common.MEDIUM_RISK:   5.0,
	common.MODERATE_RISK: 5.0,
	common.HIGH_RISK:     8.0,
	common.CRITICAL_RISK: 10.0,
}

var riskLevelMap = map[string]int{
	common.LOW_RISK:          0,
	common.MEDIUM_RISK:       1,
	common.MODERATE_RISK:     1,
	common.HIGH_RISK:         2,
	common.CRITICAL_RISK:     3,
	common.NOTEVALUATED_RISK: 5,
	common.NONE_RISK:         -1,
}

var costRelatedIncidents = map[string]struct{}{
	"Accounts Without Budget Alerts": {},
	"No Cost Chargeback":             {},
}

var identityEntityType = map[string]struct{}{
	"ADUser":               {},
	"ServiceAccount":       {},
	"IAMRole":              {},
	"GraphApplication":     {},
	"AppRegistration":      {},
	"User":                 {},
	"GCPUser":              {},
	"TrustedEntity":        {},
	"ssoUser":              {},
	"rootUser":             {},
	"openAIServiceAccount": {},
	"openAIUser":           {},
}

var parentRscEntityType = map[string]struct{}{
	common.AWS_ACCOUNT_RESOURCE_TYPE:        {},
	common.AWS_ORGUNIT_RESOURCE_TYPE:        {},
	common.AWS_ORG_RESOURCE_TYPE:            {},
	common.AZURE_TENANT_RESOURCE_TYPE:       {},
	common.AZURE_MGMTGRP_RESOURCE_TYPE:      {},
	common.AZURE_SUBSCRIPTION_RESOURCE_TYPE: {},
	common.AZURE_RG_RESOURCE_TYPE:           {},
	common.GCP_ORG_RESOURCE_TYPE:            {},
	common.GCP_FOLDER_RESOURCE_TYPE:         {},
	common.GCP_PROJECT_RESOURCE_TYPE:        {},
}

var identityHsSeverity = map[string]string{
	"exUser":         common.HIGH_RISK,
	"partnerExUser":  common.CRITICAL_RISK,
	"nonIamExUsers":  common.HIGH_RISK,
	"overPrivileged": common.CRITICAL_RISK,
	"personalEmail":  common.MODERATE_RISK,
	"MFADisabled":    common.HIGH_RISK,
	"identityOwningMoreThanOneEnterpriseApplication": common.MEDIUM_RISK,
	"serviceIdentityWithoutActiveKeys":               common.HIGH_RISK,
	"serviceIdentityWithStaleKeys":                   common.MODERATE_RISK,
	"exEmployeeOwningServiceIdentity":                common.MODERATE_RISK,
	"improperRootAccountUsage":                       common.HIGH_RISK,
}

var parentHsSeverity = map[string]string{
	"noBillingContact":                          common.LOW_RISK,
	"noSecurityContact":                         common.LOW_RISK,
	"noOperationContact":                        common.LOW_RISK,
	"noContextRG":                               common.LOW_RISK,
	"deprecatedClassicAdministrators":           common.HIGH_RISK,
	"inActiveAccounts":                          common.MEDIUM_RISK,
	"noOwnerAccounts":                           common.LOW_RISK,
	"undefinedEnvironmentAccounts":              common.MEDIUM_RISK,
	"excessiveOwnerRolesForAccountUsers":        common.HIGH_RISK,
	"singleUserOwningAccounts":                  common.MEDIUM_RISK,
	"exEmployeeOwnedAccountsWithoutBackupOwner": common.CRITICAL_RISK,
	"noResourceHierarchy":                       common.MEDIUM_RISK,
	"orgWithoutHierarchy":                       common.HIGH_RISK,
	"managementAccountWithMostResources":        common.HIGH_RISK,
	"managementAccountWithLessThanTwoUsers":     common.MEDIUM_RISK,
	"rootUserMFA":                               common.HIGH_RISK,
	"centralizeRootAccess":                      common.HIGH_RISK,
	"checkOntologyAccounts":                     common.MEDIUM_RISK,
	"accountsWithoutBudgetAlerts":               common.MEDIUM_RISK,
}

var permissionRelatedIdentityHs = map[string]struct{}{
	"MFADisabled":                      {},
	"serviceIdentityWithoutActiveKeys": {},
	"serviceIdentityWithStaleKeys":     {},
	"overPrivileged":                   {},
}

type PrioritisationData struct {
	accountValues                accountCriteriaValue
	criticalHeroStats            map[string]interface{}
	EntityType                   string
	heroStats                    []string
	IsResourceProd               bool
	IsResourceReprioritisation   bool
	IsSignificantResource        bool
	IssueSeverity                string
	IssueSeverityScore           float32
	LastCollectedAt              string
	OpenToInternet               bool
	OwnerCount                   int
	PrecizeImpact                float32
	PrecizeLikelihood            float32
	PrecizeRisk                  string
	PriorityCriteria             map[string]bool
	resourceExternalIncidentsMap map[string]int64
	resourceValues               resourceCriteriaValue
	SensitiveData                bool
	SourceImpact                 float32
	SourceLikelihood             float32
	SourceRisk                   string
	TenantID                     string
	tenantValues                 tenantCriteriaValue
	SourceScore                  float32
	IncidentDetails              IncidentDetails
	IncidentType                 string
}

var AWSImpactResourceWithLessActivity = []string{
	common.AWS_RDS_RESOURCE_TYPE,
	common.AWS_DYNAMODB_RESOURCE_TYPE,
	common.AWS_TIMESTREAMDB_RESOURCE_TYPE,
	common.AWS_TIMESTREAMTABLE_RESOURCE_TYPE,
	common.AWS_ACCOUNT_RESOURCE_TYPE,
	common.AWS_ORGUNIT_RESOURCE_TYPE,
	common.AWS_ORG_RESOURCE_TYPE,
}

var GCPImpactResourceWithLessActivity = []string{
	common.GCP_SQLDB_RESOURCE_TYPE,
	common.GCP_BIGQUERYTABLE_RESOURCE_TYPE,
	common.GCP_BIGQUERYDATASET_RESOURCE_TYPE,
	common.GCP_BIGTABLE_RESOURCE_TYPE,
	common.GCP_BIGTABLECLUSTER_RESOURCE_TYPE,
	common.GCP_SPANNERDB_RESOURCE_TYPE,
	common.GCP_SPANNERDBINSTANCE_RESOURCE_TYPE,
	common.GCP_FOLDER_RESOURCE_TYPE,
	common.GCP_PROJECT_RESOURCE_TYPE,
	common.GCP_ORG_RESOURCE_TYPE,
}

var AZUREImpactResourceWithLessActivity = []string{
	common.AZURE_MYSQL_RESOURCE_TYPE,
	common.AZURE_SQLDB_RESOURCE_TYPE,
	common.AZURE_COSMOSDB_RESOURCE_TYPE,
	common.AZURE_MARIADB_RESOURCE_TYPE,
	common.AZURE_POSTGRES_RESOURCE_TYPE,
	common.AZURE_RG_RESOURCE_TYPE,
	common.AZURE_SUBSCRIPTION_RESOURCE_TYPE,
	common.AZURE_MGMTGRP_RESOURCE_TYPE,
	common.AZURE_TENANT_RESOURCE_TYPE,
}

func SetLikelihood(currentLikelihood float32, targetLikelihoodString, criteria string, pData *PrioritisationData, inc bool) {

	targetLikelihood := likelihoodStringToNumber[targetLikelihoodString]

	final := (currentLikelihood + targetLikelihood) / 2

	if (inc && final < currentLikelihood) || (!inc && final > currentLikelihood) {
		final = currentLikelihood
	}

	pData.PriorityCriteria[criteria] = inc
	pData.PrecizeLikelihood = final
}

func SetImpact(currentImpact float32, targetImpactString, criteria string, pData *PrioritisationData, inc bool) {

	targetImpact := impactStringToNumber[targetImpactString]

	final := (currentImpact + targetImpact) / 2

	if (inc && final < currentImpact) || (!inc && final > currentImpact) {
		final = currentImpact
	}

	pData.PriorityCriteria[criteria] = inc
	pData.PrecizeImpact = final
}

func SetLikelihoodWithoutAvg(targetLikelihoodString, criteria string, pData *PrioritisationData, inc bool) {

	pData.PriorityCriteria[criteria] = inc

	currentIndex := indexOf(likelihoodLevels, targetLikelihoodString)
	if currentIndex == -1 {
		return
	}
	pData.PrecizeLikelihood = likelihoodStringToNumber[targetLikelihoodString]
}

func SetImpactWithoutAvg(targetImpactString, criteria string, pData *PrioritisationData, inc bool) {

	pData.PriorityCriteria[criteria] = inc

	currentIndex := indexOf(impactLevels, targetImpactString)
	if currentIndex == -1 {
		return
	}
	pData.PrecizeImpact = impactStringToNumber[targetImpactString]
}

func GetLikelihoodAndImpactFromRiskScore(score float32) (float32, float32) {
	var likelihood string
	var impact string

	switch {
	case score == 0:
		likelihood = common.VERY_UNLIKELY_LIKELIHOOD
		impact = common.NEGLIGIBLE_IMPACT
	case score >= 0.1 && score <= 3.9:
		likelihood = common.UNLIKELY_LIKELIHOOD
		impact = common.MINOR_IMPACT
	case score >= 4.0 && score <= 6.9:
		likelihood = common.POSSIBLE_LIKELIHOOD
		impact = common.MODERATE_IMPACT
	case score >= 7.0 && score <= 8.9:
		likelihood = common.LIKELY_LIKELIHOOD
		impact = common.SIGNIFICANT_IMPACT
	case score >= 9.0 && score <= 9.9:
		likelihood = common.VERY_LIKELY_LIKELIHOOD
		impact = common.SEVERE_IMPACT
	case score == 10.0:
		likelihood = common.VERY_LIKELY_LIKELIHOOD
		impact = common.SEVERE_IMPACT
	default:
		likelihood = common.VERY_UNLIKELY_LIKELIHOOD
		impact = common.NEGLIGIBLE_IMPACT
	}

	return likelihoodStringToNumber[likelihood], impactStringToNumber[impact]
}

var (
	likelihoodLevels = []string{common.VERY_UNLIKELY_LIKELIHOOD, common.UNLIKELY_LIKELIHOOD, common.POSSIBLE_LIKELIHOOD, common.LIKELY_LIKELIHOOD, common.VERY_LIKELY_LIKELIHOOD}
	impactLevels     = []string{common.NEGLIGIBLE_IMPACT, common.MINOR_IMPACT, common.MODERATE_IMPACT, common.SIGNIFICANT_IMPACT, common.SEVERE_IMPACT}
	riskLevels       = []string{common.LOW_RISK, common.MEDIUM_RISK, common.HIGH_RISK, common.CRITICAL_RISK}
)

func indexOf(slice []string, value string) int {
	for i, v := range slice {
		if v == value {
			return i
		}
	}
	return -1
}

func IncreaseLikelihoodByOneLevel(currentLikelihood float32, criteria string, pData *PrioritisationData, inc bool) {

	final := (currentLikelihood + (currentLikelihood + 1)) / 2

	if (inc && final < currentLikelihood) || (!inc && final > currentLikelihood) {
		final = currentLikelihood
	}

	pData.PriorityCriteria[criteria] = inc
	pData.PrecizeLikelihood = final
}

func DecreaseLikelihoodByOneLevel(currentLikelihood float32, criteria string, pData *PrioritisationData, inc bool) {

	if currentLikelihood == 0 {
		return
	}
	final := (currentLikelihood + (currentLikelihood - 1)) / 2

	if (inc && final < currentLikelihood) || (!inc && final > currentLikelihood) {
		final = currentLikelihood
	}

	pData.PriorityCriteria[criteria] = inc
	pData.PrecizeLikelihood = final
}

func IncreaseImpactByOneLevel(currentImpact float32, criteria string, pData *PrioritisationData, inc bool) {

	final := (currentImpact + (currentImpact + 1)) / 2

	if (inc && final < currentImpact) || (!inc && final > currentImpact) {
		final = currentImpact
	}

	pData.PriorityCriteria[criteria] = inc
	pData.PrecizeImpact = final
}

func DecreaseImpactByOneLevel(currentImpact float32, criteria string, pData *PrioritisationData, inc bool) {

	final := (currentImpact + (currentImpact - 1)) / 2

	if (inc && final < currentImpact) || (!inc && final > currentImpact) {
		final = currentImpact
	}

	pData.PriorityCriteria[criteria] = inc
	pData.PrecizeImpact = final
}

func DecreaseRiskByOneLevel(currentRisk string) string {

	currentIndex := indexOf(riskLevels, currentRisk)
	if currentIndex > 0 {
		return riskLevels[currentIndex-1]
	}
	return currentRisk
}

func IncreaseRiskByOneLevel(currentRisk string) string {

	currentIndex := indexOf(riskLevels, currentRisk)
	if currentIndex < len(riskLevels)-1 {
		return riskLevels[currentIndex+1]
	}
	return currentRisk
}

func GetRiskDetails(likelihood, impact string) (string, float32) {

	var riskLevel string

	if likelihood == common.VERY_UNLIKELY_LIKELIHOOD {
		if impact == common.NEGLIGIBLE_IMPACT || impact == common.MINOR_IMPACT || impact == common.MODERATE_IMPACT {
			riskLevel = common.LOW_RISK
		} else if impact == common.SIGNIFICANT_IMPACT {
			riskLevel = common.MEDIUM_RISK
		} else if impact == common.SEVERE_IMPACT {
			riskLevel = common.MEDIUM_RISK
		}
	} else if likelihood == common.UNLIKELY_LIKELIHOOD {
		if impact == common.NEGLIGIBLE_IMPACT || impact == common.MINOR_IMPACT {
			riskLevel = common.LOW_RISK
		} else if impact == common.MODERATE_IMPACT || impact == common.SIGNIFICANT_IMPACT {
			riskLevel = common.MEDIUM_RISK
		} else if impact == common.SEVERE_IMPACT {
			riskLevel = common.HIGH_RISK
		}
	} else if likelihood == common.POSSIBLE_LIKELIHOOD {
		if impact == common.NEGLIGIBLE_IMPACT {
			riskLevel = common.LOW_RISK
		} else if impact == common.MINOR_IMPACT || impact == common.MODERATE_IMPACT {
			riskLevel = common.MEDIUM_RISK
		} else if impact == common.SIGNIFICANT_IMPACT {
			riskLevel = common.HIGH_RISK
		} else if impact == common.SEVERE_IMPACT {
			riskLevel = common.CRITICAL_RISK
		}
	} else if likelihood == common.LIKELY_LIKELIHOOD {
		if impact == common.NEGLIGIBLE_IMPACT || impact == common.MINOR_IMPACT {
			riskLevel = common.MEDIUM_RISK
		} else if impact == common.MODERATE_IMPACT || impact == common.SIGNIFICANT_IMPACT {
			riskLevel = common.HIGH_RISK
		} else if impact == common.SEVERE_IMPACT {
			riskLevel = common.CRITICAL_RISK
		}
	} else if likelihood == common.VERY_LIKELY_LIKELIHOOD {
		if impact == common.NEGLIGIBLE_IMPACT {
			riskLevel = common.MEDIUM_RISK
		} else if impact == common.MINOR_IMPACT || impact == common.MODERATE_IMPACT {
			riskLevel = common.HIGH_RISK
		} else if impact == common.SIGNIFICANT_IMPACT || impact == common.SEVERE_IMPACT {
			riskLevel = common.CRITICAL_RISK
		}
	} else {
		return "Unknown", 0.0
	}

	return riskLevel, riskScoreMap[riskLevel]
}

func IsResourceOpenToInternet(pData *PrioritisationData) {

	openToInternetHeroStats := map[string]interface{}{
		"publiclyExposedSageMaker":  struct{}{},
		"publiclyFoundIp":           struct{}{},
		"externallyExposedIPWithDB": struct{}{},
		"unsecuredAIServices":       struct{}{},
		"exposedIpWithVuln":         struct{}{},
		"externallyExposedIp":       struct{}{},
		"noContextPublicS3":         struct{}{},
		"publicExposedLambda":       struct{}{},
		"noContextPublicSA":         struct{}{},
		"publiclyExposedFunction":   struct{}{},
		"publicIpAddressNoContext":  struct{}{},
		"openFirewall":              struct{}{},
		"publiclySQLDBs":            struct{}{},
		"disableLocalAuth":          struct{}{},
	}

	for _, hsKey := range pData.heroStats {
		if _, ok := openToInternetHeroStats[hsKey]; ok {
			pData.OpenToInternet = true
			return
		}
	}

}

func GetLikelihoodAndImpactFromRiskLevel(riskLevel string) (float32, float32) {

	var likelihood, impact string

	switch riskLevel {
	case common.LOW_RISK:
		likelihood = common.VERY_UNLIKELY_LIKELIHOOD
		impact = common.NEGLIGIBLE_IMPACT
	case common.MEDIUM_RISK:
		likelihood = common.POSSIBLE_LIKELIHOOD
		impact = common.MODERATE_IMPACT
	case common.MODERATE_RISK:
		likelihood = common.POSSIBLE_LIKELIHOOD
		impact = common.MODERATE_IMPACT
	case common.HIGH_RISK:
		likelihood = common.LIKELY_LIKELIHOOD
		impact = common.SIGNIFICANT_IMPACT
	case common.CRITICAL_RISK:
		likelihood = common.VERY_LIKELY_LIKELIHOOD
		impact = common.SEVERE_IMPACT
	default:
		likelihood = common.VERY_UNLIKELY_LIKELIHOOD
		impact = common.NEGLIGIBLE_IMPACT
	}

	return likelihoodStringToNumber[likelihood], impactStringToNumber[impact]
}

func SetRisk(currentRisk, targetRisk string) string {

	targetRiskIndex := indexOf(riskLevels, targetRisk)
	if targetRiskIndex >= 0 {
		return riskLevels[targetRiskIndex]
	}
	return currentRisk

}

func GeneratePrecizeIncidentRepriorDesc(highImpactCriteria, lowImpactCriteria []string,
	highLikelihoodCriteria, lowLikelihoodCriteria []string,
	highRiskCriteria, lowRiskCriteria []string, incidentDataMap common.Incident, pData PrioritisationData) string {

	description := ""

	if incidentDataMap.PrecizeRisk == common.HIGH_RISK || incidentDataMap.PrecizeRisk == common.CRITICAL_RISK {
		additionalContext, err := generateCategoryIntroWithGPT(pData.IncidentDetails.Categories, incidentDataMap.TenantID)
		if err == nil && additionalContext != "" {
			additionalContext = strings.TrimSpace(additionalContext)
			description += additionalContext
			if !strings.HasSuffix(description, ".") {
				description += "."
			}
		}
	}

	if len(description) > 0 {
		description += "\\n"
	}
	description += "The risk identified by Precize depends on the likelihood of an event occurring and the impact of such an event if occurred. In this scenario, "

	if incidentDataMap.SeverityDiff != 0 && incidentDataMap.SeverityDiff != 2 {
		if len(highLikelihoodCriteria) > 0 {
			if incidentDataMap.SeverityDiff == 1 {
				description += "the likelihood is higher "
			} else {
				incidentDataMap.SeverityDiff = -1
				description += "the likelihood is lower "
			}
			description += "since this resource "
			for i, criteria := range highLikelihoodCriteria {

				description += criteria

				if len(highLikelihoodCriteria) > 1 && i == len(highLikelihoodCriteria)-2 {
					description += " and "
				} else if i != len(highLikelihoodCriteria)-1 {
					description += ", "
				} else {
					description += "."
				}
			}
		}

		if len(highLikelihoodCriteria) > 0 && len(highImpactCriteria) > 0 {
			description += "The "
		} else if len(highImpactCriteria) > 0 {
			description += "the "
		}

		if len(highImpactCriteria) > 0 {
			if incidentDataMap.SeverityDiff == 1 {
				description += "impact is higher "
			} else {
				incidentDataMap.SeverityDiff = -1
				description += "impact is lower "
			}

			description += "since this resource "
			for i, criteria := range highImpactCriteria {

				description += criteria

				if len(highImpactCriteria) > 1 && i == len(highImpactCriteria)-2 {
					description += " and "
				} else if i != len(highImpactCriteria)-1 {
					description += ", "
				} else {
					description += "."
				}
			}
		}
	} else {
		if len(highLikelihoodCriteria) > 0 {
			description += "the event is likely to occur since the resource "
			for i, criteria := range highLikelihoodCriteria {
				description += criteria
				if len(highLikelihoodCriteria) > 1 && i == len(highLikelihoodCriteria)-2 {
					description += " and "
				} else if i != len(highLikelihoodCriteria)-1 {
					description += ", "
				} else {
					description += "."
				}
			}
		}

		if len(highImpactCriteria) > 0 {
			if len(highLikelihoodCriteria) <= 0 {
				description += "the "
			} else {
				description += " The "
			}
			description += "potential impact is considerable since the resource "

			for i, criteria := range highImpactCriteria {
				description += criteria
				if len(highImpactCriteria) > 1 && i == len(highImpactCriteria)-2 {
					description += " and "
				} else if i != len(highImpactCriteria)-1 {
					description += ", "
				} else {
					description += "."
				}
			}
		}
	}

	if len(lowImpactCriteria) > 0 || len(lowLikelihoodCriteria) > 0 {
		if len(highImpactCriteria) > 0 || len(highLikelihoodCriteria) > 0 {
			description += " Besides, the "
		} else {
			description += "the "
		}

		description += "account that this resource belongs to, "

		lowCriteria := append(lowLikelihoodCriteria, lowImpactCriteria...)

		for i, criteria := range lowCriteria {

			description += criteria

			if len(lowCriteria) > 1 && i == len(lowCriteria)-2 {
				description += " and "
			} else if i != len(lowCriteria)-1 {
				description += ", "
			}
		}

		description += " which has also contributed to the likelihood and impact of the event."
	}

	if len(highRiskCriteria) > 0 || len(lowRiskCriteria) > 0 {

		description += " The risk has also been effected because the resource "

		if pData.IncidentType == VULNERABILITY_TYPE && pData.OpenToInternet {
			description += "is critically vulnerable, being externally accessible and exposed to potential malware attacks and Remote Code Execution (RCE) risks, "
		} else if pData.IncidentType == VULNERABILITY_TYPE {
			description += "is vulnerable and may be susceptible to potential malware attacks, "
		} else if pData.OpenToInternet {
			description += "is externally accessible, potentially exposing it to Remote Code Execution (RCE) risks and external exploit vectors, "
		}

		riskCriteria := append(highRiskCriteria, lowRiskCriteria...)

		for i, criteria := range riskCriteria {

			description += criteria

			if len(riskCriteria) > 1 && i == len(riskCriteria)-2 {
				description += " and "
			} else if i != len(riskCriteria)-1 {
				description += ", "
			} else {
				description += "."
			}
		}
	}

	if incidentDataMap.PrecizeRisk == incidentDataMap.SourceRisk {
		description += " While certain factors increased the likelihood and impact of the event, other factors reduced them, ultimately keeping the overall risk unchanged."
	}

	return description
}

func DeleteDuplicateCriteria(priorityCriteria map[string]bool) map[string]bool {

	// Prevent prod env clause duplication
	if _, ok := priorityCriteria[IMPACTFULRESOURCE_CRITERIA]; ok {
		delete(priorityCriteria, PRODENV_CRITERIA)
	}

	if _, ok := priorityCriteria[MODERATEIMPACTFULRESOURCE_CRITERIA]; ok {
		delete(priorityCriteria, PRODENV_CRITERIA)
	}

	if _, ok := priorityCriteria[RSC_VULN_OPENTOINTERNET]; ok {
		delete(priorityCriteria, OPENTOINTERNET_CRITERIA)
	}

	if _, ok := priorityCriteria[RSC_VULN_NOTOPENTOINTERNET]; ok {
		delete(priorityCriteria, NOTOPENTOINTERNET_CRITERIA)
	}

	if _, ok := priorityCriteria[OPENTOINTERNET_NOT_ENCRYPTED_CRITERIA]; ok {
		delete(priorityCriteria, OPENTOINTERNET_CRITERIA)
	}

	return priorityCriteria
}

func CustomRound(value, roundOffValue float64) float64 {
	decimalPart := value - math.Floor(value)
	if decimalPart <= roundOffValue {
		return math.Floor(value)
	}
	return math.Ceil(value)
}

func GeneratePrecizeCostIncidentRepriorDesc(highRiskCriteria []string, lowRiskCriteria []string, incidentDataMap common.Incident) string {
	joinCriteria := func(criteria []string) string {
		if len(criteria) == 0 {
			return ""
		}
		if len(criteria) == 1 {
			return criteria[0]
		}
		return strings.Join(criteria[:len(criteria)-1], ", ") + " and " + criteria[len(criteria)-1]
	}

	if incidentDataMap.SeverityDiff == 2 {
		if incidentDataMap.PrecizeRisk == common.HIGH_RISK || incidentDataMap.PrecizeRisk == common.CRITICAL_RISK {
			highRiskPart := joinCriteria(highRiskCriteria)
			if highRiskPart != "" {
				return fmt.Sprintf("Precize Risk is high, since the account %s", joinCriteria(highRiskCriteria))
			}
		}
		lowRiskPart := joinCriteria(lowRiskCriteria)
		if lowRiskPart != "" {
			return fmt.Sprintf("Precize Risk is low, since the account %s", joinCriteria(lowRiskCriteria))
		}
	}

	switch incidentDataMap.SeverityDiff {
	case 1:
		// Risk increased
		highRiskPart := joinCriteria(highRiskCriteria)
		if highRiskPart != "" {
			return fmt.Sprintf("Precize Risk is high, since the account %s", highRiskPart)
		}

	case -1:
		// Risk decreased
		lowRiskPart := joinCriteria(lowRiskCriteria)
		if lowRiskPart != "" {
			return fmt.Sprintf("Precize Risk is low, since the account %s", joinCriteria(lowRiskCriteria))
		}

	case 0:
		// Risk unchanged
		highRiskPart := joinCriteria(highRiskCriteria)
		lowRiskPart := joinCriteria(lowRiskCriteria)

		desc := ""
		if highRiskPart != "" {
			desc = fmt.Sprintf("Precize Risk is high since the account %s. ",
				highRiskPart)
		}
		if len(lowRiskCriteria) > 0 {
			desc += fmt.Sprintf("However, the account %s, ultimately keeping the overall risk unchanged", lowRiskPart)
		}

		return desc

	default:
		return ""
	}

	return ""
}

func GetRiskLevel(score float32) string {
	switch {
	case score > 10.0:
		return common.CRITICAL_RISK
	case score >= 8.0:
		return common.HIGH_RISK
	case score >= 5.0:
		return common.MEDIUM_RISK
	case score >= 2.0:
		return common.LOW_RISK
	default:
		return common.LOW_RISK
	}
}

func generateCategoryIntroWithGPT(categories []string, tenantID string) (string, error) {

	categoriesJoined := strings.Join(categories, ", ")

	categoryDefinitions := `
		- "Vulnerability": A configuration issue existing with the resource which can be utilized by malware to breach in.
		- "Malware": The resource is already infected with malware/virus.
		- "Governance": A safety measure that will help in the future and is not an active issue as of now.
		- "SensitiveData": The data in the resource is sensitive.
		- "OpenToInternet": The resource is already open to internet and can be accessed by everyone.
		- "SensitiveDataExposed": The data in the resource is sensitive and has been exposed.
	`

	prompt := []common.OpenAIMessage{
		{
			Role:    "system",
			Content: "You are a cybersecurity expert. Generate a professional, concise introduction (1-2 sentences) for an incident report that mentions the following categories. Each incident can be part of more than one category. The introduction should sound professional, technically accurate, and reflect the severity implied by the combination of categories." + categoryDefinitions,
		},
		{
			Role:    "user",
			Content: fmt.Sprintf("Generate a concise introduction for an incident/security issue involving these categories: %s, Don't mention the criticality of the incident just generate a generic decsription based on the category", categoriesJoined),
		},
	}

	descStr := common.DeriveIncidentIntroDescFromCategories(categoriesJoined, tenantID, prompt)

	if descStr == "" {
		return "", nil
	}

	return descStr, nil
}
