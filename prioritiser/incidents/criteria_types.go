package incidents

import (
	"sync"
)

type Criteria struct {
	tenantValues  sync.Map
	accountValues sync.Map
	resourceValue sync.Map
}

type tenantCriteriaValue struct {
	resourceCount     int64
	accountCosts      map[string]float64
	identityHeroStats map[string]map[string]interface{}
}

type accountCriteriaValue struct {
	resourceCount        int64
	activitiesCount      int64
	totalEnvCount        int64
	prodEnvCount         int64
	noOwnersCount        int64
	totalOwnersCount     int64
	uniqueOwnersCount    int64
	exEmployeesCount     int64
	impactResourcesCount int64
	costCenters          []string
}

type resourceCriteriaValue struct {
	activitiesCount int64
}

func (sc *Criteria) StoreTenantValues(key string, value tenantCriteriaValue) {
	sc.tenantValues.Store(key, value)
}

func (sc *Criteria) LoadTenantValues(key string) (tenantCriteriaValue, bool) {
	if val, ok := sc.tenantValues.Load(key); ok {
		return val.(tenantCriteriaValue), true
	}
	return tenantCriteriaValue{}, false
}

func (sc *Criteria) StoreAccountValues(key string, value accountCriteriaValue) {
	sc.accountValues.Store(key, value)
}

func (sc *Criteria) LoadAccountValues(key string) (accountCriteriaValue, bool) {
	if val, ok := sc.accountValues.Load(key); ok {
		return val.(accountCriteriaValue), true
	}
	return accountCriteriaValue{}, false
}

func (sc *Criteria) StoreResourceValue(key string, value resourceCriteriaValue) {
	sc.resourceValue.Store(key, value)
}

func (sc *Criteria) LoadResourceValue(key string) (resourceCriteriaValue, bool) {
	if val, ok := sc.resourceValue.Load(key); ok {
		return val.(resourceCriteriaValue), true
	}
	return resourceCriteriaValue{}, false
}
