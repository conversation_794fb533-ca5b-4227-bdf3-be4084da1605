package orca

import (
	"encoding/json"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	ORCA_URL    = "https://api.orcasecurity.io/api/"
	LIMIT       = "1000"
	MAX_RECORDS = 10000
)

func OrcaDataCollector(tenantID, lastCollectedAt, serviceID string, orcaEnv tenant.OrcaEnvironment) error {

	var (
		accountIDToNameMap = make(map[string]string)
		accountNameList    = make([]string, 0)
		accountIdList      = make([]string, 0)
	)
	resp, err := transport.SendRequestToServer("GET", "/precize/private/"+serviceID+"/"+tenantID+"/accounts", nil, nil)
	if err != nil {
		return err
	}

	var accountResponse common.AccountResponse

	if err = json.Unmarshal(resp, &accountResponse); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
		return err
	}

	processAccounts(accountResponse.Data.Accounts, accountIDToNameMap)

	query := `{"query":{"bool":{"must":[{"term":{"collectedAt":"` + lastCollectedAt + `"}},{"match":{"entityType.keyword":"VMInstance"}}],"must_not":[],"should":[]}},"from":0,"size":0,"sort":[],"aggs":{"accId_aggregation":{"terms":{"field":"accountId.keyword","size":10000}}}}`

	rscTypeResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCES_INDEX}, query)
	if err != nil {
		return err
	}

	if accIdAggs, ok := rscTypeResp["accId_aggregation"].(map[string]interface{}); ok {
		if accIdBuckets, ok := accIdAggs["buckets"].([]interface{}); ok {
			for _, accIdBucket := range accIdBuckets {
				if accIdBucketMap, ok := accIdBucket.(map[string]interface{}); ok {
					if accId, ok := accIdBucketMap["key"].(string); ok {
						if accName, ok := accountIDToNameMap[accId]; ok {
							accountNameList = append(accountNameList, accName)
							accountIdList = append(accountIdList, accId)
						}
					}
				}
			}
		}
	}

	if len(accountNameList) > 0 {
		err := fetchOrcaAssets(tenantID, lastCollectedAt, accountNameList, accountIdList, orcaEnv, accountIDToNameMap)
		if err != nil {
			return err
		}
	}

	return nil
}

func fetchOrcaAssets(tenantID, lastCollectedAt string, accountNameList, accountIdList []string, orcaEnv tenant.OrcaEnvironment, accountIDToNameMap map[string]string) error {
	header := make(map[string]string)
	if len(orcaEnv.Token) > 0 {
		header["Authorization"] = "Token " + orcaEnv.Token
	} else {
		return nil
	}

	var (
		nextPageToken   string
		urlParams       = make(map[string]string)
		bulkUpdateQuery string
		currentCount    int
		maxRecords      = 5000
		searchAfter     interface{}
		activeResources = make(map[string]string)
	)

	for i := 0; i < len(accountIdList); i += 1000 {

		end := i + 1000
		if end > len(accountIdList) {
			end = len(accountIdList)
		}

		cloudResourceQuery := `{"_source":["entityId","entityType","accountId"],"query":{"bool":{"must":[{"term":{"collectedAt":"` + lastCollectedAt + `"}},{"terms":{"entityType.keyword":["VMInstance", "GKECluster"]}},{"terms":{"accountId.keyword":["` + strings.Join(accountIdList[i:end], `","`) + `"]}}],"must_not":[],"should":[]}}}`

		for {

			cloudRscDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, cloudResourceQuery, searchAfter)
			if err != nil {
				break
			}

			if len(cloudRscDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			for docId, cloudRscDoc := range cloudRscDocs {
				if entityId, ok := cloudRscDoc["entityId"].(string); ok {
					if entityType, ok := cloudRscDoc["entityType"].(string); ok {
						if accountID, ok := cloudRscDoc["accountId"].(string); ok {
							accountName := accountIDToNameMap[accountID]
							if len(entityId) > 0 {
								parts := strings.Split(entityId, "/")
								if len(parts) > 1 {
									activeResources[entityType+":"+accountName+":"+parts[len(parts)-1]] = docId
								}
							}
						}
					}
				}
			}
		}

		filter :=
			`{
				"filter":
				[
					{
						"field": "cloud_provider",
						"includes": "gcp"
					},
					{
						"field": "asset_type_string",
						"includes": "VM"
					},
					{
						"field": "account_name",
						"includes": ["` + strings.Join(accountNameList, `","`) + `"]
					}
				]
			}`

		for {

			urlParams["dsl_filter"] = filter

			urlParams["limit"] = LIMIT
			if len(nextPageToken) > 0 {
				urlParams["next_page_token"] = nextPageToken
			}

			response, err := transport.SendRequest("GET", ORCA_URL+"query/assets", urlParams, header, nil)
			if err != nil {
				log.Print(logger.ERROR, "Error Orca API", []string{tenantID}, urlParams, header)
				break
			}

			var alertData map[string]interface{}
			err = json.Unmarshal(response, &alertData)
			if err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				break
			}

			err = processOrcaAssets(alertData, tenantID, &bulkUpdateQuery, &currentCount, activeResources)
			if err != nil {
				break
			}

			if currentCount > maxRecords {

				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkUpdateQuery); err != nil {
					logger.Print(logger.ERROR, "Error in bulk update of cloud resources", []string{tenantID}, err)
					break
				}

				logger.Print(logger.INFO, "Cloud Resources bulk API Successful for Orca Assets for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
				currentCount = 0
				bulkUpdateQuery = ""
			}

			if hasToken, ok := alertData["has_next_page_token"].(bool); ok && hasToken {
				if token, ok := alertData["next_page_token"].(string); ok {
					nextPageToken = token
				}

				time.Sleep(1 * time.Second)
			} else {
				break
			}
		}
	}

	if currentCount > 0 {

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkUpdateQuery); err != nil {
			logger.Print(logger.ERROR, "Error in bulk update of cloud resources", []string{tenantID}, err)
			return err
		}

		logger.Print(logger.INFO, "Cloud Resource bulk API Successful for Orca Assets for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
		currentCount = 0
		bulkUpdateQuery = ""
	}

	return nil
}

func processOrcaAssets(alertData map[string]interface{}, tenantID string, bulkUpdateQuery *string, currentCount *int, activeResources map[string]string) error {

	if alertsData, ok := alertData["data"].([]interface{}); ok {

		for _, alertData := range alertsData {

			alertDataBytes, err := json.Marshal(alertData)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON:", []string{tenantID}, err)
				continue
			}

			var assetsDataMap common.OrcaAssetData
			err = json.Unmarshal(alertDataBytes, &assetsDataMap)
			if err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON:", []string{tenantID}, err)
				continue
			}
			if len(assetsDataMap.AssetType) > 0 || len(assetsDataMap.AssetTypeString) > 0 {

				var entityType string
				if entityType, ok = common.OrcaToEntityTypeMappings[assetsDataMap.AssetType]; !ok {
					entityType = common.OrcaToEntityTypeMappings[strings.ToLower(assetsDataMap.AssetTypeString)]
				}

				if cloudResourceDocId, ok := activeResources[entityType+":"+strings.ToLower(assetsDataMap.AccountName)+":"+strings.ToLower(assetsDataMap.AssetName)]; ok {

					var osType string

					if len(assetsDataMap.Compute.DistributionName) > 0 {
						osType = assetsDataMap.Compute.DistributionName
						if len(assetsDataMap.Compute.DistributionVersion) > 0 {
							osType += " " + assetsDataMap.Compute.DistributionVersion
						}
					}

					externalContentBytes, err := json.Marshal(assetsDataMap)
					if err != nil {
						logger.Print(logger.ERROR, "Error Marshalling AssetData Map", []string{tenantID}, err)
						continue
					}

					externalContextString := strings.Replace(string(externalContentBytes), `"`, `\"`, -1)

					cloudResourceUpdateMetadata := `{"update": {"_id": "` + cloudResourceDocId + `"}}`
					cloudResourceUpdateDoc := `{"script":{"source":"if (ctx._source.extContext == null) { ctx._source.extContext = [:]; } ctx._source.extContext.orcaDetails = params.orcaDetails;  ctx._source.osType = params.osType;","lang":"painless","params":{"orcaDetails":"` + externalContextString + `","osType":"` + osType + `"}}}`

					*bulkUpdateQuery += cloudResourceUpdateMetadata + "\n" + cloudResourceUpdateDoc + "\n"
					*currentCount++

					if *currentCount > MAX_RECORDS {

						if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, *bulkUpdateQuery); err != nil {
							logger.Print(logger.ERROR, "Error in bulk update of cloud resources", []string{tenantID}, err)
							return err
						}

						logger.Print(logger.INFO, "Cloud Resource bulk API Successful for Orca Assets for "+strconv.Itoa(*currentCount)+" records", []string{tenantID})
						*currentCount = 0
						*bulkUpdateQuery = ""
					}
				}
			}
		}
	}

	return nil
}

func processAccounts(accounts []common.Account, accountIDToNameMap map[string]string) {
	for _, account := range accounts {

		if len(account.Name) > 0 && len(account.EntityID) > 0 {
			accountIDToNameMap[account.EntityID] = account.Name
		}

		if account.Childs != nil && len(account.Childs) > 0 {
			processAccounts(account.Childs, accountIDToNameMap)
		}
	}
}
