package main

import (
	"encoding/json"
	"slices"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func RemoveOwnersFromRctx(tenantID, ownerName, ownerType, ownerDesc, ownerTypeDBKey, collectedAts, resourcesIds, identityID string, modifyCR bool) {
	// Remove owners from resource context for previous collectedAt's

	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"match": map[string]interface{}{
							"tenantId.keyword": tenantID,
						},
					},
					{
						"nested": map[string]interface{}{
							"path": ownerTypeDBKey,
							"query": map[string]interface{}{
								"bool": map[string]interface{}{
									"must": []map[string]interface{}{
										{
											"match": map[string]interface{}{
												ownerTypeDBKey + ".name": ownerName,
											},
										},
									},
									"should": []map[string]interface{}{
										{
											"match": map[string]interface{}{
												ownerTypeDBKey + ".desc": ownerDesc,
											},
										},
										{
											"match": map[string]interface{}{
												ownerTypeDBKey + ".type": ownerType,
											},
										},
									},
									"minimum_should_match": 1,
								},
							},
						},
					},
				},
			},
		},
	}

	mustClauses := query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"].([]map[string]interface{})

	if collectedAts != "" {
		collectedAtValues := strings.Split(collectedAts, ",")
		if len(collectedAtValues) > 0 {

			for i, v := range collectedAtValues {
				collectedAtValues[i] = strings.TrimSpace(v)
			}
			mustClauses = append(mustClauses, map[string]interface{}{
				"terms": map[string]interface{}{
					"lastCollectedAt": collectedAtValues,
				},
			})
		}
	}

	if resourcesIds != "" {
		resourceIDValues := strings.Split(resourcesIds, ",")
		if len(resourceIDValues) > 0 {

			for i, v := range resourceIDValues {
				resourceIDValues[i] = strings.TrimSpace(v)
			}
			mustClauses = append(mustClauses, map[string]interface{}{
				"terms": map[string]interface{}{
					"resourceId.keyword": resourceIDValues,
				},
			})
		}
	}

	query["query"].(map[string]interface{})["bool"].(map[string]interface{})["must"] = mustClauses

	queryBytes, err := json.Marshal(query)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling query to JSON", err)
		return
	}

	var (
		searchAfter                               interface{}
		bulkResourceContextRequest, bulkCRRequest strings.Builder
	)

	for {

		resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, string(queryBytes), searchAfter)
		if err != nil {
			return
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		var (
			crDocToOwnerMap = make(map[string][]string)
		)

		logger.Print(logger.INFO, "Resources fetched", len(resourcesDocs))

		for resourcesDocID, resourcesDoc := range resourcesDocs {
			rscDocBytes, err := json.Marshal(resourcesDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
				continue
			}

			var rscCtx common.ResourceContextInsertDoc
			if err = json.Unmarshal(rscDocBytes, &rscCtx); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
				continue
			}

			switch ownerTypeDBKey {
			case "definedOwners":
				rscCtx.ResourceOwnerTypes.DefinedOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.DefinedOwners, ownerName, ownerDesc, ownerType, crDocToOwnerMap, rscCtx)
			case "derivedOwners":
				rscCtx.ResourceOwnerTypes.DerivedOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.DerivedOwners, ownerName, ownerDesc, ownerType, crDocToOwnerMap, rscCtx)
			case "inheritedOwners":
				if rscCtx.ResourceType == common.AZURE_RG_RESOURCE_TYPE {
					rscCtx.ResourceOwnerTypes.InheritedOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.InheritedOwners, ownerName, ownerDesc, ownerType, crDocToOwnerMap, rscCtx)
				} else {
					rscCtx.ResourceOwnerTypes.InheritedOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.InheritedOwners, ownerName, ownerDesc, ownerType, nil, rscCtx)
				}
			case "codeOwners":
				rscCtx.ResourceOwnerTypes.CodeOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.CodeOwners, ownerName, ownerDesc, ownerType, nil, rscCtx)
			case "costOwners":
				rscCtx.ResourceOwnerTypes.CostOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.CostOwners, ownerName, ownerDesc, ownerType, nil, rscCtx)
			case "securityOwners":
				rscCtx.ResourceOwnerTypes.SecurityOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.SecurityOwners, ownerName, ownerDesc, ownerType, nil, rscCtx)
			case "opsOwners":
				rscCtx.ResourceOwnerTypes.OpsOwners = filterOwnersByUserLogic(rscCtx.ResourceOwnerTypes.OpsOwners, ownerName, ownerDesc, ownerType, nil, rscCtx)
			default:
				continue
			}

			resourceContextInsertMetadata := `{"index": {"_id": "` + resourcesDocID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(rscCtx)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")
		}

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful", []string{tenantID})
		bulkResourceContextRequest.Reset()

		var (
			crDocsIDs = make([]string, 0)
		)

		for crDocId, _ := range crDocToOwnerMap {
			crDocsIDs = append(crDocsIDs, crDocId)
		}

		if len(crDocToOwnerMap) > 0 && modifyCR {

			crQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"id.keyword":["` + strings.Join(crDocsIDs, `","`) + `"]}}]}}}`
			crDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery)
			if err != nil {
				return
			}

			for _, crDoc := range crDocs {

				if docID, ok := crDoc["id"].(string); ok {
					if crOwners, ok := crDoc["owner"].([]interface{}); ok {
						if rctxOwners, ok := crDocToOwnerMap[docID]; ok {
							for i, crOwner := range crOwners {
								if crOwner, ok := crOwner.(string); ok {
									if !slices.Contains(rctxOwners, crOwner) {
										crOwners = slices.Delete(crOwners, i, i+1)
									}
								}
							}

							ownerStrings := make([]string, len(crOwners))
							for i, owner := range crOwners {
								ownerStrings[i] = owner.(string)
							}
							crsUpdateMetadata := `{"update": {"_id": "` + docID + `"}}`
							crsUpdateDoc := `{"doc":{"owner":["` + strings.Join(ownerStrings, `","`) + `"]}}`
							bulkCRRequest.WriteString(crsUpdateMetadata)
							bulkCRRequest.WriteString("\n")
							bulkCRRequest.WriteString(crsUpdateDoc)
							bulkCRRequest.WriteString("\n")
						}
					}
				}
			}

			if len(bulkCRRequest.String()) > 0 {
				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCRRequest.String()); err != nil {
					return
				}
			}

			logger.Print(logger.INFO, "Cloud resources bulk API Successful", []string{tenantID})
			bulkCRRequest.Reset()
		}
	}
}

func filterOwnersByUserLogic(items []common.ResourceContextItem, nameCriteria, descCriteria, typeCriteria string, crDocToOwnerMap map[string][]string, rscCtx common.ResourceContextInsertDoc) []common.ResourceContextItem {
	var keptItems []common.ResourceContextItem
	for _, item := range items {

		shouldSkip := false
		if item.Name == nameCriteria {
			if descCriteria != "" && item.Desc == descCriteria {
				if typeCriteria == "" || item.Type == typeCriteria {
					shouldSkip = true
				}
			} else if typeCriteria != "" && item.Type == typeCriteria {
				if descCriteria == "" || item.Desc == descCriteria {
					shouldSkip = true
				}
			}
		}

		if !shouldSkip {
			keptItems = append(keptItems, item)
			if crDocToOwnerMap != nil {
				crDocId := common.GenerateCombinedHashIDCaseSensitive(rscCtx.TenantID, rscCtx.LastCollectedAt, strings.ToLower(rscCtx.Account), strings.ToLower(rscCtx.ResourceID), rscCtx.ResourceType)
				crDocToOwnerMap[crDocId] = append(crDocToOwnerMap[crDocId], item.Name)
			}
		} else {
			crDocId := common.GenerateCombinedHashIDCaseSensitive(rscCtx.TenantID, rscCtx.LastCollectedAt, strings.ToLower(rscCtx.Account), strings.ToLower(rscCtx.ResourceID), rscCtx.ResourceType)
			if crDocToOwnerMap != nil {
				if _, ok := crDocToOwnerMap[crDocId]; !ok {
					crDocToOwnerMap[crDocId] = append(crDocToOwnerMap[crDocId], "")
				}
			}
		}
	}
	return keptItems
}
