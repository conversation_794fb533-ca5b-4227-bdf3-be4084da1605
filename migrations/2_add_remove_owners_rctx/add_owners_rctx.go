package main

import (
	"encoding/json"
	"slices"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func AddOwnersToRctx(tenantID, ownerName, ownerType, ownerDesc, ownerTypeDBKey, collectedAts, resourcesIds, identityID string, modifyCR bool) {
	// Add owners to resource context for previous collectedAt's

	if len(resourcesIds) <= 0 {
		return
	}

	mustClauses := []map[string]interface{}{
		{
			"match": map[string]interface{}{
				"tenantId.keyword": tenantID,
			},
		},
		{
			"terms": map[string]interface{}{
				"resourceId.keyword": strings.Split(resourcesIds, ","),
			},
		},
	}

	if collectedAts != "" {
		collectedAtValues := strings.Split(collectedAts, ",")

		validCollectedAtValues := []string{}
		for _, v := range collectedAtValues {
			trimmedV := strings.TrimSpace(v)
			if trimmedV != "" {
				validCollectedAtValues = append(validCollectedAtValues, trimmedV)
			}
		}
		if len(validCollectedAtValues) > 0 {
			mustClauses = append(mustClauses, map[string]interface{}{
				"terms": map[string]interface{}{
					"lastCollectedAt": validCollectedAtValues,
				},
			})
		}
	}

	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": mustClauses,
			},
		},
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling query to JSON", err)
		return
	}

	var (
		bulkResourceContextRequest, bulkCRRequest strings.Builder
		crDocsIDs                                 = make([]string, 0)
		searchAfter                               interface{}
	)

	for {

		resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, string(queryBytes), searchAfter)
		if err != nil {
			return
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resources fetched", len(resourcesDocs))

		for resourcesDocID, resourcesDoc := range resourcesDocs {
			rscDocBytes, err := json.Marshal(resourcesDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
				continue
			}

			var rscCtx common.ResourceContextInsertDoc
			if err = json.Unmarshal(rscDocBytes, &rscCtx); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
				continue
			}

			rctxItem := common.ResourceContextItem{
				Name:       ownerName,
				Type:       ownerType,
				Desc:       ownerDesc,
				IdentityId: identityID,
			}

			switch ownerTypeDBKey {
			case "definedOwners":
				rscCtx.ResourceOwnerTypes.DefinedOwners = appendOwnerIfUnique(rscCtx.ResourceOwnerTypes.DefinedOwners, rctxItem)
			case "derivedOwners":
				rscCtx.ResourceOwnerTypes.DerivedOwners = appendOwnerIfUnique(rscCtx.ResourceOwnerTypes.DerivedOwners, rctxItem)
			case "inheritedOwners":
				rscCtx.ResourceOwnerTypes.InheritedOwners = appendOwnerIfUnique(rscCtx.ResourceOwnerTypes.InheritedOwners, rctxItem)
			case "codeOwners":
				rscCtx.ResourceOwnerTypes.CodeOwners = appendOwnerIfUnique(rscCtx.ResourceOwnerTypes.CodeOwners, rctxItem)
			case "costOwners":
				rscCtx.ResourceOwnerTypes.CostOwners = appendOwnerIfUnique(rscCtx.ResourceOwnerTypes.CostOwners, rctxItem)
			case "securityOwners":
				rscCtx.ResourceOwnerTypes.SecurityOwners = appendOwnerIfUnique(rscCtx.ResourceOwnerTypes.SecurityOwners, rctxItem)
			case "opsOwners":
				rscCtx.ResourceOwnerTypes.OpsOwners = appendOwnerIfUnique(rscCtx.ResourceOwnerTypes.OpsOwners, rctxItem)
			}

			crDocId := common.GenerateCombinedHashIDCaseSensitive(rscCtx.TenantID, rscCtx.LastCollectedAt, strings.ToLower(rscCtx.Account), strings.ToLower(rscCtx.ResourceID), rscCtx.ResourceType)
			crDocsIDs = append(crDocsIDs, crDocId)

			resourceContextInsertMetadata := `{"index": {"_id": "` + resourcesDocID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(rscCtx)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")

		}

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful", []string{tenantID})
		bulkResourceContextRequest.Reset()

		if modifyCR && len(crDocsIDs) > 0 {
			crQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"id.keyword":["` + strings.Join(crDocsIDs, `","`) + `"]}}]}}}`
			crDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery)
			if err != nil {
				return
			}

			for _, crDoc := range crDocs {
				if docID, ok := crDoc["id"].(string); ok {
					if resourceType, ok := crDoc["entityType"].(string); ok {
						if crOwners, ok := crDoc["owner"].([]interface{}); ok {

							skipAddition := false

							if (ownerType == "inheritedOwners" && resourceType != common.AZURE_RG_RESOURCE_TYPE) || ownerType == "costOwners" || ownerType == "securityOwners" || ownerType == "opsOwners" {
								skipAddition = true
							} else if ownerType == "derivedOwners" {
								switch resourceType {
								case common.AZURE_ADUSER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.GCP_IAM_RESOURCE_TYPE, common.AWS_ROOTUSER_RESOURCE_TYPE, common.AZURE_USEROWNER_RESOURCE_TYPE:
									skipAddition = true

								case common.AWS_IAM_USER_RESOURCE_TYPE:

									if entityJSONStr, ok := crDoc["entityJSON"].(string); ok {
										entityJSON := make(map[string]interface{})
										if err := json.Unmarshal([]byte(entityJSONStr), &entityJSON); err != nil {
											continue
										}
										if hasConsoleLogin, ok := entityJSON["hasLoginProfile"].(bool); ok {
											if hasConsoleLogin {
												skipAddition = true
											}
										}
									}

								}
							}

							if skipAddition {
								continue
							}

							ownerStrings := make([]string, len(crOwners))
							for i, owner := range crOwners {
								ownerStrings[i] = owner.(string)
							}

							if !slices.Contains(ownerStrings, ownerName) {
								ownerStrings = append(ownerStrings, ownerName)
							}

							crsUpdateMetadata := `{"update": {"_id": "` + docID + `"}}`
							crsUpdateDoc := `{"doc":{"owner":["` + strings.Join(ownerStrings, `","`) + `"]}}`
							bulkCRRequest.WriteString(crsUpdateMetadata)
							bulkCRRequest.WriteString("\n")
							bulkCRRequest.WriteString(crsUpdateDoc)
							bulkCRRequest.WriteString("\n")
						}
					}
				}
			}

			if len(bulkCRRequest.String()) > 0 {
				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCRRequest.String()); err != nil {
					return
				}
			}

			logger.Print(logger.INFO, "Cloud resource bulk API Successful", []string{tenantID})
			bulkCRRequest.Reset()
		}

	}
}

func appendOwnerIfUnique(owners []common.ResourceContextItem, newItem common.ResourceContextItem) []common.ResourceContextItem {
	for _, existingOwner := range owners {
		if existingOwner.Name == newItem.Name && existingOwner.Type == newItem.Type {
			return owners
		}
	}
	return append(owners, newItem)
}
