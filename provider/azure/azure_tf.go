package azure

import (
	"bytes"
	"encoding/json"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

type ResourceGraphRequest struct {
	Subscriptions []string `json:"subscriptions"`
	Query         string   `json:"query"`
	Options       struct {
		SkipToken string `json:"$skipToken"`
	} `json:"options"`
}

type TfResourceChangesResponse struct {
	SkipToken string `json:"$skipToken"`
	Data      []struct {
		ID             string            `json:"id"`
		ResourceGroup  string            `json:"resourceGroup"`
		Type           string            `json:"type"`
		SubscriptionID string            `json:"subscriptionId"`
		Tags           map[string]string `json:"tags"`
		ChangeTime     time.Time         `json:"changeTime"`
		Location       string            `json:"location"`
	} `json:"data"`
}

func ProcessAzureTerraformEvents(tenantID, envID, subscriptionID string, eventStartTime, eventEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.AZURE_TF_RESOURCE, tenantStartTime)

	if (eventEndTime.Sub(eventStartTime)) > eventEndTime.Sub(defaultTime) {
		eventStartTime = defaultTime
	}

	startTime := elastic.DateTime(eventStartTime)
	endTime := elastic.DateTime(eventEndTime)
	lastEventTime := eventStartTime

	searchQuery := `{"query":{"bool":{"filter":[{"range":{"eventTime":{"gt":"` + startTime + `","lte":"` + endTime + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + subscriptionID + `"}}]}}}`
	tfEventDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_ACTIVITY_INDEX}, searchQuery)
	if err != nil {
		return
	}

	if len(tfEventDocs) > 0 {
		logger.Print(logger.INFO, "Fetched azure terraform events from "+startTime+" to "+endTime, []string{tenantID, subscriptionID})
	}

	resourceIdToEventDocMap := make(map[string]common.ResourceEventCopy)
	resourceTypeToIDsMap := make(map[string][]string)
	uniqueResourceIds := make(map[string]struct{})

	for _, tfEventDoc := range tfEventDocs {

		if resourceInterfaces, ok := tfEventDoc["resources"].([]interface{}); ok {
			for _, resourceInterface := range resourceInterfaces {

				if resourceMap, ok := resourceInterface.(map[string]interface{}); ok {

					if resourceType, ok := resourceMap["resourceType"].(string); ok {

						resourceIDs := make([]string, 0)

						if resourceID, ok := resourceMap["resourceName"].(string); ok {
							if strings.Contains(resourceID, "[") && strings.Contains(resourceID, "]") {

								trimmedID := strings.Trim(resourceID, "[]")
								trimmedIDs := strings.Split(trimmedID, ",")

								resourceIDs = append(resourceIDs, trimmedIDs...)

							} else {
								resourceIDs = append(resourceIDs, resourceID)
							}
						}

						for _, resourceID := range resourceIDs {

							if cloudTrailEvent, ok := tfEventDoc["cloudTrailEvent"].(string); ok {

								if eventName, ok := tfEventDoc["eventName"].(string); ok {

									if serviceCode, ok := tfEventDoc["serviceCode"].(string); ok {

										if eventTimeStr, ok := tfEventDoc["eventTime"].(string); ok {

											if region, ok := tfEventDoc["region"].(string); ok {

												if resourceGroup, ok := tfEventDoc["resourceGroup"].(string); ok {

													eventTime, err := elastic.ParseDateTime(eventTimeStr)
													if err != nil {
														logger.Print(logger.ERROR, "Error parse date time", eventTime)
														return
													}
													if eventTime.After(lastEventTime) {
														lastEventTime = eventTime
													}

													if resourceType == "" || resourceID == "" {
														continue
													}

													if strings.Contains(strings.ToLower(eventName), "delete") {
														//TODO: Fetch From DB
														continue
													}

													eventResourceId := resourceID
													var cloudTrailEventMap map[string]interface{}

													err = json.Unmarshal([]byte(cloudTrailEvent), &cloudTrailEventMap)
													if err != nil {
														logger.Print(logger.ERROR, "Error unmarshalling JSON", cloudTrailEvent, err)
														return
													}

													if _, ok := uniqueResourceIds[resourceID]; !ok {
														uniqueResourceIds[resourceID] = struct{}{}
														if resourceIDs, ok := resourceTypeToIDsMap[resourceType]; !ok {
															ids := make([]string, 0)
															ids = append(ids, resourceID)
															resourceTypeToIDsMap[resourceType] = ids

														} else {
															resourceIDs = append(resourceIDs, resourceID)
															resourceTypeToIDsMap[resourceType] = resourceIDs
														}

														resourceIdToEventDocMap[resourceID] = common.ResourceEventCopy{
															ResourceMap:     cloudTrailEventMap,
															ResourceType:    resourceType,
															ResourceName:    resourceID,
															ServiceCode:     serviceCode,
															EventName:       eventName,
															EventTime:       eventTime,
															ResourceGroup:   resourceGroup,
															Region:          region,
															EventResourceID: eventResourceId,
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	lastEventTimeTf, err := common.MapEventToTfResources(resourceIdToEventDocMap, resourceTypeToIDsMap, common.AZURE_SERVICE_CODE, tenantID, subscriptionID, eventStartTime)
	if err != nil {
		return
	}

	if !lastEventTimeTf.IsZero() && lastEventTimeTf.After(eventEndTime) {
		lastEventTime = lastEventTimeTf
	}

	azureResourceGraphUrl := `/precize/private/azure/getResourcesByFilter/` + envID
	resourceChangesQuery := `resourcechanges |extend targetResourceId = tostring(properties.targetResourceId), changeTime = todatetime(properties.changeAttributes.timestamp) | where changeTime > todatetime('` + startTime + `') and changeTime < todatetime('` + endTime + `') | project targetResourceId, changeTime | join ( resources | extend targetResourceId=id) on targetResourceId | where isnotempty(tags['precize_git_commit']) | project id, resourceGroup, type, subscriptionId, tags, changeTime`

	var (
		resourceGraphRequest = ResourceGraphRequest{
			Subscriptions: []string{subscriptionID},
			Query:         resourceChangesQuery,
		}

		buf               bytes.Buffer
		tfResourceChanges TfResourceChangesResponse
	)

	for {

		err := json.NewEncoder(&buf).Encode(resourceGraphRequest)
		if err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID, subscriptionID}, err)
			break
		}

		getTfResourceChangesResp, err := transport.SendRequestToServer("POST", azureResourceGraphUrl, nil, &buf)
		if err != nil {
			break
		}

		if err = json.Unmarshal(getTfResourceChangesResp, &tfResourceChanges); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, subscriptionID}, err)
			break
		}

		for _, tfResourceChange := range tfResourceChanges.Data {

			var terraformResourceDoc = common.TerraformResourceDoc{
				ResourceID:    tfResourceChange.ID,
				ResourceName:  tfResourceChange.ID,
				ResourceType:  tfResourceChange.Type,
				EventTime:     elastic.DateTime(tfResourceChange.ChangeTime),
				ResourceGroup: tfResourceChange.ResourceGroup,
				Account:       tfResourceChange.SubscriptionID,
				Region:        tfResourceChange.Location,
				TenantID:      tenantID,
				CSP:           common.AZURE_SERVICE_CODE,
				Approach:      "yor",
			}

			for k, v := range tfResourceChange.Tags {
				switch k {
				case "precize_git_commit":
					terraformResourceDoc.CommitID = v
				case "precize_git_repo":
					terraformResourceDoc.Repository = v
				case "precize_git_file":
					terraformResourceDoc.Filename = v
				}
			}

			if _, err = elastic.InsertDocument(tenantID, elastic.TERRAFORM_RESOURCES_INDEX, terraformResourceDoc); err != nil {
				continue
			}

			gitUsers, err := common.FetchGitFileAuthorsForTerraform(tenantID, terraformResourceDoc.CommitID,
				terraformResourceDoc.Filename, terraformResourceDoc.ResourceID, common.AZURE_SERVICE_CODE)
			if err != nil {
				logger.Print(logger.ERROR, "Got error fetching git file authors", []string{tenantID, terraformResourceDoc.Account}, err)
				continue
			}

			for _, gitUser := range gitUsers {

				resourceEvent := common.ResourceEvent{
					ResourceID:   terraformResourceDoc.ResourceID,
					ResourceName: terraformResourceDoc.ResourceID,
					Region:       terraformResourceDoc.Region,
					Account:      terraformResourceDoc.Account,
				}

				if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
					ResourceEvent: resourceEvent,
					Action:        gitUser.Action,
					TenantID:      tenantID,
					User:          gitUser.Name,
					UserType:      gitUser.Client,
					EventTime:     gitUser.CommitTime,
					DocID:         gitUser.DocID,
				}); err != nil {
					continue
				}
			}
		}

		if len(tfResourceChanges.SkipToken) <= 0 {
			break
		}

		resourceGraphRequest.Options.SkipToken = tfResourceChanges.SkipToken
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.AZURE_TF_RESOURCE, lastEventTime)
}
