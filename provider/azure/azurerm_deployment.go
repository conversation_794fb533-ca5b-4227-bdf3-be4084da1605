package azure

import (
	"encoding/json"
	"path"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

type DeploymentExtended struct {
	Name       string `json:"name"`
	Properties struct {
		ProvisioningState string    `json:"provisioningState"`
		Timestamp         time.Time `json:"timestamp"`
		OutputResources   []struct {
			ID string `json:"id"`
		} `json:"outputResources"`
	} `json:"properties"`
}

type ExportedTemplate struct {
	Template interface{} `json:"template"`
}

type GenericResource struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Location string `json:"location"`
}

func processARMTemplate(armTemplateEvent ARMTemplateEvent, envID string) {

	var (
		resourceEvents                  []common.ResourceEvent
		deployment                      DeploymentExtended
		deploymentCompletionWaitCounter int
	)

	deploymentName := path.Base(armTemplateEvent.DeploymentID)

	for {

		getDeploymentUrl := `/precize/private/azure/getDeployment/` + envID + `/` + armTemplateEvent.Subscription +
			`/` + armTemplateEvent.ResourceGroup + `/` + deploymentName

		getDeploymentResp, err := transport.SendRequestToServer("GET", getDeploymentUrl, nil, nil)
		if err != nil {
			break
		}

		if err = json.Unmarshal(getDeploymentResp, &deployment); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{
				armTemplateEvent.TenantID, armTemplateEvent.Subscription}, err)
			break
		}

		if deployment.Properties.ProvisioningState == "Running" ||
			deployment.Properties.ProvisioningState == "Creating" ||
			deployment.Properties.ProvisioningState == "Updating" {

			time.Sleep(30 * time.Second)
			deploymentCompletionWaitCounter++
			if deploymentCompletionWaitCounter > 360 {
				logger.Print(logger.INFO, "Waited too long for deployment to complete. Timing out", []string{
					armTemplateEvent.TenantID, armTemplateEvent.Subscription}, armTemplateEvent.DeploymentID)
				return
			}

			continue
		}

		break
	}

	if deployment.Properties.ProvisioningState == "Succeeded" {

		exportTemplateUrl := `/precize/private/azure/exportTemplate/` + envID + `/` + armTemplateEvent.Subscription +
			`/` + armTemplateEvent.ResourceGroup + `/` + deploymentName

		exportTemplateResp, err := transport.SendRequestToServer("GET", exportTemplateUrl, nil, nil)
		if err != nil {
			return
		}

		var exportedTemplate ExportedTemplate

		if err = json.Unmarshal(exportTemplateResp, &exportedTemplate); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{
				armTemplateEvent.TenantID, armTemplateEvent.Subscription}, err)
			return
		}

		template, err := json.Marshal(exportedTemplate.Template)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{
				armTemplateEvent.TenantID, armTemplateEvent.Subscription}, err)
			return
		}

		minifiedJSONHash, err := common.GetMinifiedJSONFileHash(template)
		if err != nil {
			logger.Print(logger.ERROR, "Got error generating minifed json hash", []string{
				armTemplateEvent.TenantID, armTemplateEvent.Subscription}, err)
			return
		}

		for _, outputResource := range deployment.Properties.OutputResources {

			getResourceByIDUrl := `/precize/private/azure/getResourceById/` + envID

			resourceResp, err := transport.SendRequestToServer("GET", getResourceByIDUrl, map[string]string{
				"resourceId": outputResource.ID,
			}, nil)
			if err != nil {
				continue
			}

			var resource GenericResource

			if err = json.Unmarshal(resourceResp, &resource); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{
					armTemplateEvent.TenantID, armTemplateEvent.Subscription}, err)
				continue
			}

			resourceEvent := common.ResourceEvent{
				ResourceID:   outputResource.ID,
				ResourceName: resource.Name,
				ResourceType: resource.Type,
			}

			resourceEvents = append(resourceEvents, resourceEvent)

			resourceEvent.Region = resource.Location
			resourceEvent.Account = armTemplateEvent.Subscription
			resourceEvent.ResourceGroup = armTemplateEvent.ResourceGroup

			gitUsers, err := common.FetchGitFileAuthorsWithMinifiedHash(armTemplateEvent.TenantID, minifiedJSONHash)
			if err != nil {
				logger.Print(logger.ERROR, "Got error fetching git authors with minified hash", []string{
					armTemplateEvent.TenantID, armTemplateEvent.Subscription}, err)
				continue
			}

			for _, gitUser := range gitUsers {

				if _, err = elastic.InsertDocument(armTemplateEvent.TenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
					ResourceEvent: resourceEvent,
					Action:        gitUser.Action,
					TenantID:      armTemplateEvent.TenantID,
					User:          gitUser.Name,
					UserType:      gitUser.Client,
					EventTime:     gitUser.CommitTime,
					DocID:         gitUser.DocID,
				}); err != nil {
					continue
				}
			}
		}

		if _, err = elastic.InsertDocument(armTemplateEvent.TenantID, elastic.ARM_TEMPLATES_INDEX, ARMTemplateDoc{
			ARMTemplateEvent: armTemplateEvent,
			DeploymentName:   deployment.Name,
			TemplateHash:     minifiedJSONHash,
			DeploymentTime:   elastic.DateTime(deployment.Properties.Timestamp),
			ResourceEvents:   resourceEvents,
		}); err != nil {
			return
		}
	}

}
