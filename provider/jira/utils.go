package jira

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

type jiraBoardsResponse struct {
	Values []struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	} `json:"values"`
}

type jiraSprintsResponse struct {
	Values []struct {
		ID    int    `json:"id"`
		Name  string `json:"name"`
		State string `json:"state"`
	} `json:"values"`
}

func processJiraSprintValues(issueType common.JiraIssueType, issueTypeField common.JiraIssueTypeField,
	project common.JiraProject, tenantID string, jiraEnv tenant.JiraEnvironment, jiraEndTime time.Time) error {

	var (
		boardsUrl        = jiraEnv.URL + "/rest/agile/1.0/board"
		basicAuth        = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		boardsMaxResults = 50
		boardsStartAt    = 0
		parentID         = issueTypeField.FieldID
		accountID        = project.ID + "/" + issueTypeField.FieldID
	)

	for {

		boardsResp, err := transport.SendRequest(
			"GET",
			boardsUrl,
			map[string]string{"startAt": strconv.Itoa(boardsStartAt), "maxResults": strconv.Itoa(boardsMaxResults)},
			map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
			nil,
		)
		if err != nil {
			break
		}

		if boardsStartAt == 0 {
			deleteTrueQuery := `{"query":{"bool":{"must":[{"term":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"type.keyword":"` + common.USER_JIRADATA_TYPE + `"}},{"term":{"parentId.keyword":"` + parentID + `"}},{"term":{"accountId.keyword":"` + accountID + `"}}]}},"script":"ctx._source.deleted = true;"}`

			// Every iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
			logger.Print(logger.INFO, "Identifying deleted jira sprint values", []string{tenantID})
			elastic.UpdateByQuery(elastic.JIRA_DATA_INDEX, deleteTrueQuery)
		}

		var jiraBoardsResp jiraBoardsResponse

		if err = json.Unmarshal(boardsResp, &jiraBoardsResp); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
			break
		}

		if len(jiraBoardsResp.Values) <= 0 {
			break
		}

		boardsStartAt = boardsStartAt + boardsMaxResults

		for _, board := range jiraBoardsResp.Values {

			var (
				sprintsUrl        = jiraEnv.URL + "/rest/agile/1.0/board/" + strconv.Itoa(board.ID) + "/sprint"
				basicAuth         = strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
				sprintsMaxResults = 50
				sprintsStartAt    = 0
			)

			for {

				sprintsResp, err := transport.SendRequest(
					"GET",
					sprintsUrl,
					map[string]string{"startAt": strconv.Itoa(sprintsStartAt), "maxResults": strconv.Itoa(sprintsMaxResults)},
					map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
					nil,
				)
				if err != nil {
					break
				}

				var jiraSprintsResp jiraSprintsResponse

				if err = json.Unmarshal(sprintsResp, &jiraSprintsResp); err != nil {
					logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
					break
				}

				if len(jiraSprintsResp.Values) <= 0 {
					break
				}

				sprintsStartAt = sprintsStartAt + sprintsMaxResults

				var (
					recordsCount        int
					bulkJiraDataRequest string
				)

				for _, sprint := range jiraSprintsResp.Values {

					docID := common.GenerateCombinedHashIDCaseSensitive(strconv.Itoa(sprint.ID), common.FIELDVALUE_JIRADATA_TYPE, issueTypeField.FieldID, tenantID, accountID)

					additionalDetails := make(map[string]interface{})

					additionalDetails["state"] = sprint.State

					var deleted bool

					// Closed sprint cannot be assigned in tickets
					if sprint.State == "closed" {
						deleted = true
					}

					additionalDetailsString, err := json.Marshal(additionalDetails)
					if err != nil {
						continue
					}

					jiraDataDoc := common.JiraDataDoc{
						ID:                docID,
						JiraID:            strconv.Itoa(sprint.ID),
						Name:              sprint.Name,
						Type:              common.FIELDVALUE_JIRADATA_TYPE,
						ParentID:          parentID,
						AccountID:         accountID,
						TenantID:          tenantID,
						AdditionalDetails: string(additionalDetailsString),
						InsertTime:        elastic.DateTime(jiraEndTime),
						Deleted:           deleted,
					}

					jiraDataInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
					jiraDataInsertDoc, err := json.Marshal(jiraDataDoc)
					if err != nil {
						logger.Print(logger.ERROR, "Failed to marshal document", err)
						continue
					}

					recordsCount++
					bulkJiraDataRequest = bulkJiraDataRequest + jiraDataInsertMetadata + "\n" + string(jiraDataInsertDoc) + "\n"
				}

				if recordsCount > 0 {
					if err := elastic.BulkDocumentsAPI(tenantID, elastic.JIRA_DATA_INDEX, bulkJiraDataRequest); err != nil {
						return err
					}

					logger.Print(logger.INFO, "Jira data bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
				}
			}
		}
	}

	return nil
}
