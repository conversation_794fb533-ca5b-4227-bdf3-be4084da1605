package jira

import (
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	JIRA_TIME_FORMAT_QUERY = "2006-01-02 15:04"
	JIRA_TIME_FORMAT       = "2006-01-02T15:04:05.999-0700"
)

func formatJiraTime(date time.Time) string {
	return date.UTC().Format(JIRA_TIME_FORMAT)
}

func formatJiraTimeForQuery(date time.Time) string {
	return date.UTC().Format(JIRA_TIME_FORMAT_QUERY)
}

func ValidateAuth(jiraEnv tenant.JiraEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	authValidation = make(map[string]bool)

	if len(jiraEnv.Username) <= 0 {
		versionUrl := jiraEnv.URL + "/rest/api/2/version"
		bearerToken := strings.TrimSpace(jiraEnv.Token)

		if _, err = transport.SendRequest(
			"GET",
			versionUrl,
			map[string]string{},
			map[string]string{"Authorization": "Bearer " + bearerToken},
			nil,
		); err != nil {
			authValidation[jiraEnv.Token] = false
		} else {
			authValidation[jiraEnv.Token] = true
		}
	} else {

		usersUrl := jiraEnv.URL + "/rest/api/3/users/search"
		basicAuth := strings.TrimSpace(jiraEnv.Username) + ":" + strings.TrimSpace(jiraEnv.Token)
		if _, err = transport.SendRequest(
			"GET",
			usersUrl,
			map[string]string{"startAt": strconv.Itoa(0), "maxResults": strconv.Itoa(1)},
			map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
			nil,
		); err != nil {
			authValidation[jiraEnv.Token] = false
		} else {
			authValidation[jiraEnv.Token] = true
		}
	}

	return
}

func ProcessJiraIssues(tenantID string, jiraEnv tenant.JiraEnvironment, jiraStartTime, jiraEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.JIRA_ISSUE, tenantStartTime)

	if (jiraEndTime.Sub(jiraStartTime)) > jiraEndTime.Sub(defaultTime) {
		jiraStartTime = defaultTime
	}

	if len(jiraEnv.Username) <= 0 {
		if err := processJiraDataCenterIssues(tenantID, jiraEnv, jiraStartTime, jiraEndTime); err != nil {
			return
		}
	} else {
		if err := processJiraCloudIssues(tenantID, jiraEnv, jiraStartTime, jiraEndTime); err != nil {
			return
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.JIRA_ISSUE, jiraEndTime)
}

const (
	jiraUserBufferTime = 24 * time.Hour
)

func ProcessJiraData(tenantID string, jiraEnv tenant.JiraEnvironment, jiraStartTime, jiraEndTime time.Time) {

	if jiraEndTime.Sub(jiraStartTime) < jiraUserBufferTime {
		return
	}

	if len(jiraEnv.Username) <= 0 {
		if err := processJiraDataCenterData(tenantID, jiraEnv, jiraStartTime, jiraEndTime); err != nil {
			return
		}
	} else {
		if err := processJiraCloudData(tenantID, jiraEnv, jiraStartTime, jiraEndTime); err != nil {
			return
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.JIRA_DATA, jiraEndTime)
}
