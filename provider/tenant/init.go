package tenant

import (
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	TENANT_PROCESSING                = "tenantProcessing"
	STACK_TEMPLATE_EVENT             = "stackTemplateEvent"
	ARM_TEMPLATE                     = "armTemplate"
	GITHUB_COMMIT                    = "gitCommit"
	GITLAB_COMMIT                    = "gitlabCommit"
	GITLAB_PIPELINE                  = "gitlabPipeline"
	BITBUCKET_COMMIT                 = "bitbucketCommit"
	JIRA_ISSUE                       = "jiraIssue"
	JIRA_DATA                        = "jiraData"
	AWS_TF_RESOURCE                  = "awsTfResource"
	AZURE_TF_RESOURCE                = "azureTfResource"
	GCP_TF_RESOURCE                  = "gcpTfResource"
	OKTA_APP                         = "oktaApp"
	OKTA_GROUP                       = "oktaGroup"
	OKTA_USER                        = "oktaUser"
	OKTA_EVENT                       = "oktaEvent"
	ORCA_ALERT                       = "orcaAlert"
	ORCA_ASSET                       = "orcaAsset"
	DEFENDER_REC                     = "defenderRec"
	OPENAI_RESOURCES                 = "openAIResources"
	SECHUB_FINDINGS                  = "securityHubFindings"
	SECURITY_COMMAND_CENTER_FINDINGS = "secCommandCenterFindings"
	PRISMA_ALERT                     = "prismaAlert"
	WIZ_ISSUE                        = "wizIssue"
	WIZ_VULN                         = "wizVuln"
	WIZ_RESOURCE                     = "wizRsc"
)

var (
	providerAttributeFetchProgress map[string]map[string]time.Time
	providerAttributeMutex         sync.Mutex

	providerAttributeFetchDefaults = map[string]func(t time.Time) time.Time{
		TENANT_PROCESSING:    func(t time.Time) time.Time { return time.Time{} },
		GITHUB_COMMIT:        func(t time.Time) time.Time { return t.AddDate(0, 0, -1) },
		GITLAB_COMMIT:        func(t time.Time) time.Time { return t.AddDate(0, 0, -1) },
		GITLAB_PIPELINE:      func(t time.Time) time.Time { return t.AddDate(0, -1, 0) },
		BITBUCKET_COMMIT:     func(t time.Time) time.Time { return t.AddDate(0, 0, -1) },
		JIRA_ISSUE:           func(t time.Time) time.Time { return t.AddDate(0, -3, 0) },
		JIRA_DATA:            func(t time.Time) time.Time { return time.Time{} },
		OKTA_APP:             func(t time.Time) time.Time { return time.Time{} },
		OKTA_GROUP:           func(t time.Time) time.Time { return time.Time{} },
		OKTA_USER:            func(t time.Time) time.Time { return time.Time{} },
		OKTA_EVENT:           func(t time.Time) time.Time { return t.Add(time.Duration(-1) * time.Hour) },
		ORCA_ALERT:           func(t time.Time) time.Time { return time.Time{} },
		ORCA_ASSET:           func(t time.Time) time.Time { return time.Time{} },
		DEFENDER_REC:         func(t time.Time) time.Time { return time.Time{} },
		SECHUB_FINDINGS:      func(t time.Time) time.Time { return time.Time{} },
		STACK_TEMPLATE_EVENT: func(t time.Time) time.Time { return t.Add(time.Duration(-2) * time.Hour) },
		ARM_TEMPLATE:         func(t time.Time) time.Time { return t.Add(time.Duration(-2) * time.Hour) },
		AWS_TF_RESOURCE:      func(t time.Time) time.Time { return t.Add(time.Duration(-2) * time.Hour) },
		AZURE_TF_RESOURCE:    func(t time.Time) time.Time { return t.Add(time.Duration(-2) * time.Hour) },
		GCP_TF_RESOURCE:      func(t time.Time) time.Time { return t.Add(time.Duration(-2) * time.Hour) },
		OPENAI_RESOURCES:     func(t time.Time) time.Time { return time.Time{} },
		SECURITY_COMMAND_CENTER_FINDINGS: func(t time.Time) time.Time {
			// security command center stores findings upto 18 months
			return t.AddDate(0, -18, 0)
		},
		WIZ_ISSUE:    func(t time.Time) time.Time { return time.Time{} },
		WIZ_VULN:     func(t time.Time) time.Time { return time.Time{} },
		WIZ_RESOURCE: func(t time.Time) time.Time { return time.Time{} },
	}

	providerAttributesFetchInit = map[string]func(string) (string, string, string){
		TENANT_PROCESSING: func(tenantID string) (query, index, field string) {
			return
		},
		GITHUB_COMMIT: func(tenantID string) (query, index, field string) {
			query = `{"_source":["commitTime"],"sort":[{"commitTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"gitClient.keyword":"github"}}]}},"size":1}`
			index = elastic.IAC_GIT_COMMITS_INDEX
			field = "commitTime"
			return
		},
		GITLAB_COMMIT: func(tenantID string) (query, index, field string) {
			query = `{"_source":["commitTime"],"sort":[{"commitTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"gitClient.keyword":"gitlab"}}]}},"size":1}`
			index = elastic.IAC_GIT_COMMITS_INDEX
			field = "commitTime"
			return
		},
		GITLAB_PIPELINE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["eventTime"],"sort":[{"eventTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"userType.keyword":"gitlab"}},{"match":{"action.keyword":"deployer"}}]}},"size":1}`
			index = elastic.RESOURCE_USER_EVENTS_INDEX
			field = "eventTime"
			return
		},
		BITBUCKET_COMMIT: func(tenantID string) (query, index, field string) {
			query = `{"_source":["commitTime"],"sort":[{"commitTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"gitClient.keyword":"bitbucket"}}]}},"size":1}`
			index = elastic.IAC_GIT_COMMITS_INDEX
			field = "commitTime"
			return
		},
		JIRA_ISSUE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["createdTime"],"sort":[{"createdTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}},"size":1}`
			index = elastic.JIRA_ISSUES_INDEX
			field = "createdTime"
			return
		},
		JIRA_DATA: func(tenantID string) (query, index, field string) {
			return
		},
		OKTA_EVENT: func(tenantID string) (query, index, field string) {
			query = `{"_source":["publishedTime"],"sort":[{"publishedTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"idpType.keyword":"` + common.OKTA_IDP_TYPE + `"}}]}},"size":1}`
			index = elastic.IDP_EVENTS_INDEX
			field = "publishedTime"
			return
		},
		OKTA_APP: func(tenantID string) (query, index, field string) {
			return
		},
		OKTA_GROUP: func(tenantID string) (query, index, field string) {
			return
		},
		OKTA_USER: func(tenantID string) (query, index, field string) {
			return
		},
		ORCA_ALERT: func(tenantID string) (query, index, field string) {
			query = `{"_source":["updatedAt"],"sort":[{"updatedAt":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.ORCA_SOURCE + `"}}]}},"size":1}`
			index = elastic.CLOUD_INCIDENTS_INDEX
			field = "updatedAt"
			return
		},
		ORCA_ASSET: func(tenantID string) (query, index, field string) {
			query = `{"_source":["insertTime"],"sort":[{"insertTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.ORCA_SOURCE + `"}}]}},"size":1}`
			index = elastic.EXTERNAL_CLOUD_RESOURCES_INDEX
			field = "insertTime"
			return
		},
		DEFENDER_REC: func(tenantID string) (query, index, field string) {
			query = `{"_source":["updatedAt"],"sort":[{"updatedAt":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.DEFENDER_SOURCE + `"}}]}},"size":1}`
			index = elastic.CLOUD_INCIDENTS_INDEX
			field = "updatedAt"
			return
		},
		SECHUB_FINDINGS: func(tenantID string) (query, index, field string) {
			query = `{"_source":["updatedAt"],"sort":[{"updatedAt":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.SECURITYHUB_SOURCE + `"}}]}},"size":1}`
			index = elastic.CLOUD_INCIDENTS_INDEX
			field = "updatedAt"
			return
		},
		STACK_TEMPLATE_EVENT: func(tenantID string) (query, index, field string) {
			query = `{"_source":["eventTime"],"sort":[{"eventTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}},"size":1}`
			index = elastic.CFSTACK_TEMPLATES_INDEX
			field = "eventTime"
			return
		},
		AWS_TF_RESOURCE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["eventTime"],"sort":[{"eventTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"csp.keyword":"aws"}}]}},"size":1}`
			index = elastic.TERRAFORM_RESOURCES_INDEX
			field = "eventTime"
			return
		},
		ARM_TEMPLATE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["deploymentTime"],"sort":[{"deploymentTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}},"size":1}`
			index = elastic.ARM_TEMPLATES_INDEX
			field = "deploymentTime"
			return
		},
		AZURE_TF_RESOURCE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["eventTime"],"sort":[{"eventTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"csp.keyword":"azure"}}]}},"size":1}`
			index = elastic.TERRAFORM_RESOURCES_INDEX
			field = "eventTime"
			return
		},
		GCP_TF_RESOURCE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["eventTime"],"sort":[{"eventTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"csp.keyword":"gcp"}}]}},"size":1}`
			index = elastic.TERRAFORM_RESOURCES_INDEX
			field = "eventTime"
			return
		},
		OPENAI_RESOURCES: func(tenantID string) (query, index, field string) {
			query = `{"_source":["collectedAt"],"sort":[{"collectedAt":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"deleted":"false"}}]}},"size":1}`
			index = elastic.AI_RESOURCES_INDEX
			field = "collectedAt"
			return
		},
		SECURITY_COMMAND_CENTER_FINDINGS: func(tenantID string) (query, index, field string) {
			query = `{"_source":["updatedAt"],"sort":[{"updatedAt":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.SECURITY_COMMAND_CENTER_SOURCE + `"}}]}},"size":1}`
			index = elastic.CLOUD_INCIDENTS_INDEX
			field = "updatedAt"
			return
		},
		WIZ_ISSUE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["insertTime"],"sort":[{"insertTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.WIZ_SOURCE + `"}},{"wildcard":{"additionalData":"*Issues*"}}]}},"size":1}`
			index = elastic.CLOUD_INCIDENTS_INDEX
			field = "insertTime"
			return
		},
		WIZ_VULN: func(tenantID string) (query, index, field string) {
			query = `{"_source":["insertTime"],"sort":[{"insertTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.WIZ_SOURCE + `"}},{"wildcard":{"additionalData":"*Vulnerability*"}}]}},"size":1}`
			index = elastic.CLOUD_INCIDENTS_INDEX
			field = "insertTime"
			return
		},
		WIZ_RESOURCE: func(tenantID string) (query, index, field string) {
			query = `{"_source":["insertTime"],"sort":[{"insertTime":{"order":"desc"}}],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source.keyword":"` + common.WIZ_SOURCE + `"}}]}},"size":1}`
			index = elastic.EXTERNAL_CLOUD_RESOURCES_INDEX
			field = "insertTime"
			return
		},
	}
)

func InitTenantsProviders() {

	logger.Print(logger.INFO, "Initializing tenant providers")
	providerAttributeFetchProgress = make(map[string]map[string]time.Time)

	tenantIDs, err := GetAllTenantIDs()
	if err != nil {
		return
	}

	for _, tenantID := range tenantIDs {

		for attribute, fetchInit := range providerAttributesFetchInit {

			query, index, field := fetchInit(tenantID)

			var lastTime time.Time

			if len(query) > 0 {

				docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{index}, query)
				if err != nil {
					continue
				}

				if len(docs) == 1 {

					for _, doc := range docs {

						value := doc[field]
						if lastTimeString, ok := value.(string); ok {
							lastTime, err = time.Parse(elastic.DATE_FORMAT, lastTimeString)
							if err != nil {
								logger.Print(logger.ERROR, "Got error parsing last time", []string{tenantID}, err)
								// return
							}
							lastTime = lastTime.Add(1 * time.Second)

						} else if unixTimeInt, ok := value.(float64); ok {
							// Case 2: Unix timestamp format (float64)

							lastTime = time.Unix(int64(unixTimeInt), 0).UTC()
							lastTime = lastTime.Add(1 * time.Second)
						}
					}
				}
			}

			CheckAndUpdateLastFetchTimeForTenant(tenantID, attribute, lastTime)
		}
	}

	logger.Print(logger.INFO, "Initialized tenant providers")
}

func CheckAndUpdateLastFetchTimeForTenant(tenantID, providerAttribute string, updatedTime time.Time) {

	providerAttributeMutex.Lock()

	if _, ok := providerAttributeFetchProgress[tenantID]; !ok {
		providerAttributeFetchProgress[tenantID] = make(map[string]time.Time)
	}

	if updatedTime.After(providerAttributeFetchProgress[tenantID][providerAttribute]) || providerAttributeFetchProgress[tenantID][providerAttribute].IsZero() {
		providerAttributeFetchProgress[tenantID][providerAttribute] = updatedTime
	}

	providerAttributeMutex.Unlock()
}
