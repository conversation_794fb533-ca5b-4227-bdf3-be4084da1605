package tfcloud

const (
	DEFAULT_DOMAIN = "https://app.terraform.io"
)

// Not Being Used Today
// func ProcessTerraformCloudData(tenantID string, tfCloudAccount tenant.TFCloudAccount, tfCloudStartTime, tfCloudEndTime time.Time) {

// 	config := &tfe.Config{
// 		Address:           DEFAULT_DOMAIN,
// 		Token:             tfCloudAccount.Token,
// 		RetryServerErrors: true,
// 	}

// 	if len(tfCloudAccount.Domain) > 0 {
// 		config.Address = tfCloudAccount.Domain
// 	}

// 	logger.Print(logger.INFO, "Fetching tfcloud state events from "+common.DateTime(tfCloudStartTime)+" to "+common.DateTime(tfCloudEndTime), []string{tenantID})

// 	client, err := tfe.NewClient(config)
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error connecting to tfcloud client", []string{tenantID}, err)
// 		return
// 	}

// 	var organizationsPageNumber = 1

// 	for {

// 		orgMap := make(map[string]struct{})

// 		for _, r := range tfCloudAccount.Organizations {
// 			orgMap[r] = struct{}{}
// 		}

// 		organizations, err := client.Organizations.List(context.TODO(),
// 			&tfe.OrganizationListOptions{
// 				ListOptions: tfe.ListOptions{
// 					PageNumber: organizationsPageNumber,
// 					PageSize:   20,
// 				},
// 			})
// 		if err != nil {
// 			logger.Print(logger.ERROR, "Got error fetching tfcloud organizations", []string{tenantID}, err)
// 			break
// 		}

// 		for _, organization := range organizations.Items {

// 			if _, ok := orgMap[organization.Name]; !ok {
// 				continue
// 			}

// 			var workspacesPageNumber = 1

// 			for {

// 				workspaces, err := client.Workspaces.List(context.TODO(), organization.Name,
// 					&tfe.WorkspaceListOptions{
// 						ListOptions: tfe.ListOptions{
// 							PageNumber: workspacesPageNumber,
// 							PageSize:   20,
// 						},
// 					})
// 				if err != nil {
// 					logger.Print(logger.ERROR, "Got error fetching tfcloud workspaces", []string{tenantID, organization.Name}, err)
// 					break
// 				}

// 				for _, workspace := range workspaces.Items {

// 					var stateVersionsPageNumber = 1

// 					for {

// 						stateVersions, err := client.StateVersions.List(context.TODO(),
// 							&tfe.StateVersionListOptions{
// 								Workspace:    workspace.Name,
// 								Organization: organization.Name,
// 								ListOptions: tfe.ListOptions{
// 									PageNumber: stateVersionsPageNumber,
// 									PageSize:   20,
// 								},
// 							})
// 						if err != nil {
// 							logger.Print(logger.ERROR, "Got error fetching state versions", []string{
// 								tenantID, organization.Name, workspace.Name}, err)
// 							break
// 						}

// 						for _, stateVersion := range stateVersions.Items {

// 							if stateVersion.CreatedAt.After(tfCloudStartTime) && stateVersion.CreatedAt.Before(tfCloudEndTime) {

// 								err = processStateVersion(client, stateVersion, workspace.Name, organization.Name, tenantID)
// 								if err != nil {
// 									continue
// 								}
// 							}
// 						}

// 						if stateVersions.NextPage == 0 {
// 							break
// 						}

// 						stateVersionsPageNumber = stateVersions.NextPage
// 					}
// 				}

// 				if workspaces.NextPage == 0 {
// 					break
// 				}

// 				workspacesPageNumber = workspaces.NextPage
// 			}
// 		}

// 		if organizations.NextPage == 0 {
// 			break
// 		}

// 		organizationsPageNumber = organizations.NextPage
// 	}

// 	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.TFCLOUD_TFSTATEVERSION, tfCloudEndTime)
// }
