package types

type WizCloudResourcesResponse struct {
	Data struct {
		CloudResources struct {
			Nodes    []WizCloudResource `json:"nodes"`
			PageInfo struct {
				HasNextPage bool   `json:"hasNextPage"`
				EndCursor   string `json:"endCursor"`
			} `json:"pageInfo"`
		} `json:"cloudResources"`
	} `json:"data"`
}

type WizCloudResource struct {
	ID                     string  `json:"id"`
	Name                   string  `json:"name"`
	Type                   string  `json:"type"`
	SubscriptionID         *string `json:"subscriptionId"`
	SubscriptionExternalID *string `json:"subscriptionExternalId"`
	GraphEntity            struct {
		ID               string `json:"id"`
		ProviderUniqueID string `json:"providerUniqueId"`
		Name             string `json:"name"`
		Type             string `json:"type"`
		Projects         []struct {
			ID string `json:"id"`
		} `json:"projects"`
		Properties map[string]interface{} `json:"properties"`
		FirstSeen  string                 `json:"firstSeen"`
		LastSeen   string                 `json:"lastSeen"`
	} `json:"graphEntity"`
}

type CloudResource struct {
	ID                string                 `json:"id"`
	TenantID          string                 `json:"tenantId"`
	ServiceID         int                    `json:"serviceId,omitempty"`
	AccountID         string                 `json:"accountId"`
	CollectedAt       int64                  `json:"collectedAt,omitempty"`
	EntityID          string                 `json:"entityId"`
	EntityType        string                 `json:"entityType"`
	ResourceName      string                 `json:"resourceName"`
	EntityJson        string                 `json:"entityJson"`
	Tags              []CRTag                `json:"tags"`
	CreatedDate       string                 `json:"createdDate,omitempty"`
	Location          GeoPoint               `json:"location,omitempty"`
	TagsCount         int                    `json:"tagsCount,omitempty"`
	IsTagValueEmail   bool                   `json:"isTagValueEmail,omitempty"`
	GovernanceStatus  int                    `json:"governanceStatus"`
	RiskScore         float64                `json:"riskScore"`
	RiskStatus        int                    `json:"riskStatus"`
	Region            string                 `json:"region"`
	Owner             []string               `json:"owner"`
	OwnerExistence    bool                   `json:"ownerExistence,omitempty"`
	Environment       []string               `json:"environment"`
	Deployment        []string               `json:"deployment"`
	Team              []string               `json:"team"`
	Project           []string               `json:"project"`
	Purpose           []string               `json:"purpose"`
	Client            []string               `json:"client"`
	TTL               []string               `json:"ttl"`
	Software          []string               `json:"software"`
	Deleted           bool                   `json:"deleted,omitempty"`
	HeroStatsCount    int64                  `json:"heroStatsCount"`
	ResourceGroup     string                 `json:"resourceGroup"`
	HeroStat          []string               `json:"heroStat"`
	StageCompleted    []string               `json:"stageCompleted"`
	App               []string               `json:"app"`
	MonthlyCost       float64                `json:"monthlyCost"`
	HeroStatData      []string               `json:"heroStatData"`
	Compliance        []string               `json:"compliance"`
	CostCenter        []string               `json:"costCenter"`
	Sensitivity       []string               `json:"sensitivity"`
	RelatedResources  []RelatedResource      `json:"relatedResources"`
	Migrated          bool                   `json:"migrated"`
	RandomID          int64                  `json:"randomID,omitempty"`
	ExtContext        map[string]interface{} `json:"extContext"`
	OSType            string                 `json:"osType"`
	Users             []string               `json:"users"`
	UserAgent         []string               `json:"userAgent"`
	DataEnrichmentIds []int                  `json:"dataEnrichmentIds"`
	Source            string                 `json:"source"`
}

type CRTag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type RelatedResource struct {
	ResourceDocID string `json:"resourceDocId"`
	ResourceID    string `json:"resourceId"`
	ResourceType  string `json:"resourceType"`
}

type GeoPoint struct {
	Lat float64 `json:"lat"`
	Lon float64 `json:"lon"`
}
