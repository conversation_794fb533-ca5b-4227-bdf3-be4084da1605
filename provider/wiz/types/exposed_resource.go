package types

type WizNetworkExposureResponse struct {
	Data struct {
		NetworkResources struct {
			Nodes    []NetworkExposure `json:"nodes"`
			PageInfo struct {
				HasNextPage bool   `json:"hasNextPage"`
				EndCursor   string `json:"endCursor"`
			} `json:"pageInfo"`
		} `json:"networkExposures"`
	} `json:"data"`
}

type NetworkExposure struct {
	ID            string `json:"id"`
	ExposedEntity struct {
		ID         string                 `json:"id"`
		Name       string                 `json:"name"`
		Type       string                 `json:"type"`
		Properties map[string]interface{} `json:"properties"`
	} `json:"exposedEntity"`
	AccessibleFrom struct {
		ID         string `json:"id"`
		Name       string `json:"name"`
		Type       string `json:"type"`
		Properties struct {
			VertexID                string            `json:"_vertexID"`
			CloudPlatform           string            `json:"cloudPlatform"`
			CloudProviderURL        string            `json:"cloudProviderURL"`
			ConnectedToOnPrem       bool              `json:"connectedToOnPrem"`
			ExternalID              string            `json:"externalId"`
			GatewayType             string            `json:"gatewayType"`
			HasRouteTable           bool              `json:"hasRouteTable"`
			Name                    string            `json:"name"`
			NativeType              string            `json:"nativeType"`
			ProviderUniqueID        string            `json:"providerUniqueId"`
			Region                  string            `json:"region"`
			ResourceGroupExternalID *string           `json:"resourceGroupExternalId"`
			Status                  string            `json:"status"`
			SubscriptionExternalID  string            `json:"subscriptionExternalId"`
			Tags                    map[string]string `json:"tags"`
			UpdatedAt               string            `json:"updatedAt"`
			Zone                    *string           `json:"zone"`
		} `json:"properties"`
	} `json:"accessibleFrom"`
	Path []struct {
		ID         string `json:"id"`
		Name       string `json:"name"`
		Type       string `json:"type"`
		Properties struct {
			VertexID                                  string            `json:"_vertexID"`
			AccessibleFromInternet                    bool              `json:"accessibleFrom.internet,omitempty"`
			Architecture                              string            `json:"architecture,omitempty"`
			CloudPlatform                             string            `json:"cloudPlatform"`
			CloudProviderURL                          string            `json:"cloudProviderURL"`
			CreationDate                              string            `json:"creationDate,omitempty"`
			DeploymentCoverageSensorAPISecurityStatus string            `json:"deploymentCoverage_sensor_apiSecurity_deploymentStatus,omitempty"`
			DeploymentCoverageSensorDeploymentStatus  string            `json:"deploymentCoverage_sensor_deploymentStatus,omitempty"`
			DeploymentCoverageSensorRecommendedType   *string           `json:"deploymentCoverage_sensor_recommendedSensorType,omitempty"`
			DeploymentCoverageSensorSupported         bool              `json:"deploymentCoverage_sensor_supported,omitempty"`
			ExternalID                                string            `json:"externalId,omitempty"`
			FullResourceName                          *string           `json:"fullResourceName"`
			HasAccessToSensitiveData                  bool              `json:"hasAccessToSensitiveData,omitempty"`
			HasAdminPrivileges                        bool              `json:"hasAdminPrivileges,omitempty"`
			HasHighPrivileges                         bool              `json:"hasHighPrivileges,omitempty"`
			HasSensitiveData                          bool              `json:"hasSensitiveData,omitempty"`
			InstanceType                              string            `json:"instanceType,omitempty"`
			IsContainerHost                           bool              `json:"isContainerHost,omitempty"`
			IsEphemeral                               bool              `json:"isEphemeral,omitempty"`
			IsManaged                                 bool              `json:"isManaged,omitempty"`
			MaxExposureLevel                          int               `json:"maxExposureLevel,omitempty"`
			MemoryGB                                  float64           `json:"memoryGB,omitempty"`
			Name                                      string            `json:"name"`
			NativeType                                string            `json:"nativeType"`
			NumAddressesOpenForHTTP                   uint32            `json:"numAddressesOpenForHTTP,omitempty"`
			NumAddressesOpenForHTTPS                  uint32            `json:"numAddressesOpenForHTTPS,omitempty"`
			NumAddressesOpenForNonStandardPorts       int               `json:"numAddressesOpenForNonStandardPorts,omitempty"`
			NumAddressesOpenForRDP                    int               `json:"numAddressesOpenForRDP,omitempty"`
			NumAddressesOpenForSSH                    uint32            `json:"numAddressesOpenForSSH,omitempty"`
			NumAddressesOpenForWINRM                  int               `json:"numAddressesOpenForWINRM,omitempty"`
			NumAddressesOpenToInternet                uint32            `json:"numAddressesOpenToInternet,omitempty"`
			NumPortsOpenToInternet                    int               `json:"numPortsOpenToInternet,omitempty"`
			OpenToAllInternet                         bool              `json:"openToAllInternet,omitempty"`
			OperatingSystem                           string            `json:"operatingSystem,omitempty"`
			PasswordAuthDisabled                      bool              `json:"passwordAuthDisabled,omitempty"`
			ProviderUniqueID                          string            `json:"providerUniqueId"`
			Region                                    string            `json:"region"`
			RegionLocation                            string            `json:"regionLocation,omitempty"`
			ResourceGroupExternalID                   *string           `json:"resourceGroupExternalId"`
			Status                                    string            `json:"status"`
			SubscriptionExternalID                    string            `json:"subscriptionExternalId"`
			Tags                                      map[string]string `json:"tags,omitempty"`
			TotalDisks                                int               `json:"totalDisks,omitempty"`
			UpdatedAt                                 string            `json:"updatedAt"`
			VCPUs                                     int               `json:"vCPUs,omitempty"`
			ValidatedOpenPorts                        interface{}       `json:"validatedOpenPorts,omitempty"`
			HasScreenshot                             bool              `json:"hasScreenshot,omitempty"`
			HTTPContentType                           *string           `json:"httpContentType,omitempty"`
			HTTPGETStatus                             string            `json:"httpGETStatus,omitempty"`
			HTTPTitleSnippet                          *string           `json:"httpTitleSnippet,omitempty"`
			Zone                                      *string           `json:"zone"`
			MACAddress                                string            `json:"macAddress,omitempty"`
			AddressRanges                             interface{}       `json:"addressRanges,omitempty"`
			DirectlyInternetFacing                    bool              `json:"directlyInternetFacing,omitempty"`
			HasDeployedInstances                      bool              `json:"hasDeployedInstances,omitempty"`
			IsDefault                                 bool              `json:"isDefault,omitempty"`
		} `json:"properties"`
	} `json:"path"`
	SourceIPRange        string   `json:"sourceIpRange"`
	DestinationIPRange   string   `json:"destinationIpRange"`
	PortRange            string   `json:"portRange"`
	AppProtocols         []string `json:"appProtocols"`
	NetworkProtocols     []string `json:"networkProtocols"`
	CustomIPRanges       *string  `json:"customIPRanges"`
	FirstSeenAt          string   `json:"firstSeenAt"`
	ApplicationEndpoints []struct {
		ID         string `json:"id"`
		Name       string `json:"name"`
		Type       string `json:"type"`
		Properties struct {
			VertexID                 string  `json:"_vertexID"`
			AllPorts                 bool    `json:"allPorts"`
			CloudPlatform            string  `json:"cloudPlatform"`
			CloudProviderURL         *string `json:"cloudProviderURL"`
			ExposureLevelDescription string  `json:"exposureLevel_description"`
			ExposureLevelName        string  `json:"exposureLevel_name"`
			ExposureLevelValue       int     `json:"exposureLevel_value"`
			ExternalID               string  `json:"externalId"`
			FinalHost                string  `json:"finalHost"`
			FinalPort                int     `json:"finalPort"`
			FullResourceName         *string `json:"fullResourceName"`
			HasSensitiveData         bool    `json:"hasSensitiveData"`
			Host                     string  `json:"host"`
			Name                     string  `json:"name"`
			NativeType               *string `json:"nativeType"`
			Path                     *string `json:"path"`
			Port                     int     `json:"port"`
			PortEnd                  int     `json:"portEnd"`
			PortRange                bool    `json:"portRange"`
			PortStart                int     `json:"portStart"`
			PortValidationResult     string  `json:"portValidationResult"`
			Protocol                 *string `json:"protocol"`
			Protocols                string  `json:"protocols"`
			ProviderUniqueID         *string `json:"providerUniqueId"`
			Region                   *string `json:"region"`
			ResourceGroupExternalID  *string `json:"resourceGroupExternalId"`
			Status                   *string `json:"status"`
			SubscriptionExternalID   string  `json:"subscriptionExternalId"`
			UpdatedAt                string  `json:"updatedAt"`
			Zone                     *string `json:"zone"`
		} `json:"properties"`
	} `json:"applicationEndpoints"`
	Type string `json:"type"`
}
