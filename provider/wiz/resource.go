package wiz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	extCr "github.com/precize/common/ext_cr"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	wizCommon "github.com/precize/provider/wiz/common"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/provider/wiz/types"
	"github.com/precize/transport"
)

func CollectCloudResources(tenantID, token string, wizEnv tenant.WizEnvironment, startTime, endTime, tenantStartTime time.Time, tenantData tenant.TenantData) {

	if endTime.Sub(startTime) < resourceBufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.WIZ_RESOURCE, tenantStartTime)

	if (endTime.Sub(startTime)) > endTime.Sub(defaultTime) {
		startTime = defaultTime
	}

	// startTime = time.Time{}

	logger.Print(logger.INFO, "Starting Wiz Cloud Resources collection from "+common.DateTime(startTime)+" to "+common.DateTime(endTime), []string{tenantID}, endTime.UnixMilli())

	var (
		hasNextPage     bool = true
		cursor          string
		batchSize       int = 500
		bulkInsertQuery string
		currentCount    int
	)

	headers := map[string]string{
		"accept":        "application/json",
		"authorization": "Bearer " + token,
		"content-Type":  "application/json",
	}

	enabledCsps := make(map[string]bool)

	if len(tenantData.AWSAccounts.Environment) > 0 || true {
		enabledCsps["aws"] = true
	}

	if len(tenantData.AzureAccounts.Environment) > 0 || true {
		enabledCsps["azure"] = true
	}

	if len(tenantData.GCPAccounts.Environment) > 0 || true {
		enabledCsps["gcp"] = true
	}

	logger.Print(logger.INFO, "Starting Wiz cloud resources collection", []string{tenantID})

	for hasNextPage {
		variables := map[string]interface{}{
			"first": batchSize,
			"filterBy": map[string]interface{}{
				"updatedAt": map[string]string{
					"before": elastic.DateTime(endTime),
					"after":  elastic.DateTime(startTime),
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.CloudResourceSearchQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
		}

		var result types.WizCloudResourcesResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
		}

		processWizResources(tenantID, result.Data.CloudResources.Nodes, enabledCsps, &bulkInsertQuery, &currentCount, endTime, false)

		if len(bulkInsertQuery) > 0 {
			if err = elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of external cloud resources", []string{tenantID}, err, bulkInsertQuery)

				currentCount = 0
				bulkInsertQuery = ""

				continue
			}

			logger.Print(logger.INFO, "External Cloud resources bulk API Successful for ", []string{tenantID}, currentCount)
			bulkInsertQuery = ""
			currentCount = 0
		}

		hasNextPage = result.Data.CloudResources.PageInfo.HasNextPage
		cursor = result.Data.CloudResources.PageInfo.EndCursor

		logger.Print(logger.INFO, fmt.Sprintf("Collected %d resources from Wiz", len(result.Data.CloudResources.Nodes)),
			[]string{tenantID})

		time.Sleep(500 * time.Millisecond)
	}

	hasNextPage = true
	cursor = ""

	logger.Print(logger.INFO, "Starting Wiz cloud resources deletion collection", []string{tenantID})

	for hasNextPage {
		variables := map[string]interface{}{
			"first": batchSize,
			"filterBy": map[string]interface{}{
				"deletedAt": map[string]string{
					"before": elastic.DateTime(endTime),
					"after":  elastic.DateTime(startTime),
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.CloudResourceSearchQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
		}

		var result types.WizCloudResourcesResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
		}

		processWizResources(tenantID, result.Data.CloudResources.Nodes, enabledCsps, &bulkInsertQuery, &currentCount, endTime, true)

		if len(bulkInsertQuery) > 0 {
			if err = elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of external cloud resources", []string{tenantID}, err, bulkInsertQuery)

				currentCount = 0
				bulkInsertQuery = ""

				continue
			}

			logger.Print(logger.INFO, "External Cloud resources bulk API Successful for ", []string{tenantID}, currentCount)
			bulkInsertQuery = ""
			currentCount = 0
		}

		hasNextPage = result.Data.CloudResources.PageInfo.HasNextPage
		cursor = result.Data.CloudResources.PageInfo.EndCursor

		logger.Print(logger.INFO, fmt.Sprintf("Collected %d resources from Wiz", len(result.Data.CloudResources.Nodes)),
			[]string{tenantID})

		time.Sleep(500 * time.Millisecond)
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.WIZ_RESOURCE, endTime)

	return
}

func processWizResources(tenantID string, resources []types.WizCloudResource, enabledCsps map[string]bool, bulkInsertQuery *string, currentCount *int, endTime time.Time, isDeleted bool) error {

	logger.Print(logger.INFO, "Starting Wiz resource processing for batch of "+strconv.Itoa(len(resources))+" records", []string{tenantID})

	for _, resource := range resources {

		if err := storeWizResource(tenantID, resource, enabledCsps, bulkInsertQuery, currentCount, endTime, isDeleted); err != nil {
			logger.Print(logger.ERROR, "Error processing Wiz resource %s", []string{tenantID}, resource.ID, err)
			continue
		}
	}

	logger.Print(logger.INFO, "Completed Wiz resource processing for batch of "+strconv.Itoa(len(resources))+" records", []string{tenantID})

	return nil
}

func storeWizResource(tenantID string, resource types.WizCloudResource, enabledCsps map[string]bool, bulkInsertQuery *string, currentCount *int, endTime time.Time, isDeleted bool) error {

	if cloudPlatform, ok := resource.GraphEntity.Properties["cloudPlatform"].(string); ok {
		if enabledCsps[strings.ToLower(cloudPlatform)] {

			serviceCode := common.CspStrToIdIntMap[strings.ToLower(cloudPlatform)]
			accountID := ""
			region := ""
			resourceID := ""
			resourceType := ""
			resourceName := resource.Name

			if accID, ok := resource.GraphEntity.Properties["subscriptionExternalId"].(string); ok {
				accountID = accID
			}

			if reg, ok := resource.GraphEntity.Properties["region"].(string); ok {
				region = reg
			}

			var tags []extCr.Tag
			if tagsMap, ok := resource.GraphEntity.Properties["tags"].(map[string]interface{}); ok {
				for key, value := range tagsMap {
					tags = append(tags, extCr.Tag{
						Key:   key,
						Value: fmt.Sprintf("%v", value),
					})

				}
			}

			entityJson, err := json.Marshal(resource.GraphEntity)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return err
			}

			resourceID = resource.GraphEntity.ProviderUniqueID

			if len(resourceID) <= 0 {
				if rscID, ok := resource.GraphEntity.Properties["externalId"].(string); ok {
					resourceID = strings.ToLower(rscID)
				}
			}

			if natType, ok := resource.GraphEntity.Properties["nativeType"].(string); ok {
				resourceType = natType
			}

			if len(resourceType) <= 0 {
				resourceType = resource.GraphEntity.Type
			}

			resourceID, resourceType = wizCommon.FetchResourceIDAndType(resource.GraphEntity.Properties, accountID, region, resourceID, resourceType, resourceName, resource.GraphEntity.Type)

			if len(resourceID) <= 0 {
				return nil
			}

			createdDate := ""
			if created, ok := resource.GraphEntity.Properties["creationDate"].(string); ok {
				createdDate = created
			}

			updatedDate := ""
			if updated, ok := resource.GraphEntity.Properties["updatedAt"].(string); ok {
				updatedDate = updated
			}

			resourceDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, fmt.Sprintf("%d", serviceCode), accountID, resourceID, resourceType)

			externalResource := extCr.ExternalCloudResource{
				ResourceGroup: wizCommon.ExtractRgFromEntityID(resourceID),
				ID:            resourceDocID,
				EntityType:    resourceType,
				AccountID:     accountID,
				Region:        region,
				ServiceID:     serviceCode,
				EntityID:      resourceID,
				Deleted:       isDeleted,
				TenantID:      tenantID,
				EntityJson:    string(entityJson),
				CreatedDate:   createdDate,
				UpdatedDate:   updatedDate,
				InsertTime:    elastic.DateTime(endTime),
				Source:        common.WIZ_SOURCE,
				ResourceName:  resourceName,
			}

			externalRscInsertMetadata := `{"index": {"_id": "` + resourceDocID + `"}}`
			externalRscInsertDoc, err := json.Marshal(externalResource)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return err
			}

			*bulkInsertQuery = *bulkInsertQuery + externalRscInsertMetadata + "\n" + string(externalRscInsertDoc) + "\n"
			*currentCount++
		}
	}

	return nil
}

// Not being used
func syncCloudResources(tenantID string, latestUpdateResourceDocIDs, deletedResourceDocIDs map[string]struct{}, endTime time.Time) error {

	crQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source":"wiz"}}]}},"size":0,"aggs":{"by_collectedAt":{"terms":{"field":"collectedAt","order":{"_key":"desc"},"size":2}}}}`
	crAggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery)
	if err != nil {
		return err
	}

	penultimateCollectedAt := ""

	if crAggsResp, ok := crAggsResp["by_collectedAt"].(map[string]interface{}); ok {
		if buckets, ok := crAggsResp["buckets"].([]interface{}); ok {
			if len(buckets) > 1 {
				if bucket, ok := buckets[1].(map[string]interface{}); ok {
					if keyFtr, ok := bucket["key"].(float64); ok {

						key := int64(keyFtr)
						penultimateCollectedAt = strconv.FormatInt(key, 10)

					}
				}
			}
		}
	}

	var (
		searchAfter     interface{}
		bulkInsertQuery string
		currentCount    int
	)

	crQuery = `{"query":{"bool":{"must":[{"match":{"source.keyword":"wiz"}},{"match":{"collectedAt":"` + penultimateCollectedAt + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	for {
		crDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery, searchAfter)
		if err != nil {
			return err
		}

		if len(crDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for crDocID, crDoc := range crDocs {

			if _, ok := latestUpdateResourceDocIDs[crDocID]; ok {
				continue
			}

			crDocBytes, err := json.Marshal(crDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
				continue
			}

			var crDocStruct types.CloudResource
			if err = json.Unmarshal(crDocBytes, &crDocStruct); err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
				continue
			}

			if _, ok := deletedResourceDocIDs[crDocStruct.EntityID]; ok {
				continue
			}

			crDocStruct.CollectedAt = endTime.UnixMilli()
			crDocStruct.ID = common.GenerateCombinedHashIDCaseSensitive(tenantID, fmt.Sprintf("%d", crDocStruct.CollectedAt), crDocStruct.AccountID, crDocStruct.EntityID, crDocStruct.EntityType)

			cloudResourcesInsertMetadata := `{"index": {"_id": "` + crDocStruct.ID + `"}}`
			cloudResourcesInsertDoc, err := json.Marshal(crDocStruct)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return err
			}

			bulkInsertQuery = bulkInsertQuery + cloudResourcesInsertMetadata + "\n" + string(cloudResourcesInsertDoc) + "\n"
			currentCount++
		}

		if len(bulkInsertQuery) > 0 {
			if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of cloud resources", []string{tenantID}, err, bulkInsertQuery)

				currentCount = 0
				bulkInsertQuery = ""

				continue
			}

			logger.Print(logger.INFO, "Cloud resources bulk API Successful for ", []string{tenantID}, currentCount)
			bulkInsertQuery = ""
			currentCount = 0
		}
	}

	return nil
}
