package wiz

import (
	"strings"

	"github.com/precize/common"
)

func FetchResourceIDAndType(resourceProperties map[string]interface{}, accountID, region, rscID, rscType, resourceName, wizType string) (resourceID, resourceType string) {

	resourceID = rscID
	resourceType = rscType

	nativeType := ""

	if natType, ok := resourceProperties["nativeType"].(string); ok {
		nativeType = natType
	}

	switch wizType {
	case ACCESSROLE_WIZ_TYPE:

		switch nativeType {
		case CLUSTERROLE_WIZ_NATIVE_TYPE:
			if kubernetesFlavour, ok := resourceProperties["kubernetes_kubernetesFlavor"].(string); ok {
				switch kubernetesFlavour {
				case "EKS":
					resourceType = common.AWS_EKSCLUSTERROLE_RESOURCE_TYPE
					resourceID = resourceName

					return
				case "AKS":
					// TODO

					return
				case "GKE":
					if clusterName, ok := resourceProperties["kubernetes_clusterName"].(string); ok {
						resourceType = common.GCP_GKECLUSTERROLE_RESOURCE_TYPE
						resourceID = "projects/" + accountID + "/locations/" + region + "/clusters/" + clusterName + "k8s/rbac.authorization.k8s.io/clusterroles/" + resourceName

						return
					}
				}
			}

		case ROLE_WIZ_NATIVE_TYPE:
			if kubernetesFlavour, ok := resourceProperties["kubernetes_kubernetesFlavor"].(string); ok {
				switch kubernetesFlavour {
				case "EKS":
					resourceType = common.AWS_EKSNAMESPACEROLE_RESOURCE_TYPE
					resourceID = resourceName

					return
				case "AKS":
					// TODO

					return
				case "GKE":
					if clusterName, ok := resourceProperties["kubernetes_clusterName"].(string); ok {
						if namespace, ok := resourceProperties["namespace"].(string); ok {
							resourceType = common.GCP_GKENAMESPACEROLE_RESOURCE_TYPE
							resourceID = "projects/" + accountID + "/locations/" + region + "/clusters/" + clusterName + "namespaces/" + namespace + "rbac.authorization.k8s.io/roles/" + resourceName

							return
						}
					}
				}
			}
		}
	}

	return
}

func ExtractRgFromEntityID(entityID string) (rg string) {
	rg = entityID
	entIDSplit := strings.Split(entityID, "/providers/")
	if len(entIDSplit) > 1 {
		rg = entIDSplit[0]
	}
	return
}
