package orca

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	commonExtCr "github.com/precize/common/ext_cr"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

type UnsupportedData struct {
	AssetTypes        map[string]struct{}
	EntityIDNormalize map[string]struct{}
	EntityJson        map[string]struct{}
}

func NewUnsupportedData() *UnsupportedData {
	return &UnsupportedData{
		AssetTypes:        make(map[string]struct{}),
		EntityIDNormalize: make(map[string]struct{}),
		EntityJson:        make(map[string]struct{}),
	}
}

func ProcessOrcaAssets(tenantID string, csp string, orcaEnv tenant.OrcaEnvironment, orcaAssetStartTime, orcaAssetEndTime, tenantStartTime time.Time) {

	if orcaAssetEndTime.Sub(orcaAssetStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.ORCA_ASSET, tenantStartTime)

	if (orcaAssetEndTime.Sub(orcaAssetStartTime)) > orcaAssetEndTime.Sub(defaultTime) {
		orcaAssetStartTime = defaultTime
	}

	var (
		crsQuery          = `{"_source":["resourceName", "entityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_PROJECT_RESOURCE_TYPE + `", "` + common.GCP_FOLDER_RESOURCE_TYPE + `", "` + common.GCP_ORG_RESOURCE_TYPE + `"]}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		searchAfter       interface{}
		parentRscNameToID = make(map[string]string)
	)

	for {
		crsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(crsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, crsDoc := range crsDocs {
			if resourceName, ok := crsDoc["resourceName"].(string); ok {
				if entityID, ok := crsDoc["entityId"].(string); ok {
					if len(resourceName) > 0 && len(entityID) > 0 {
						parentRscNameToID[resourceName] = entityID
					}
				}
			}
		}
	}

	if err := fetchOrcaAssets(tenantID, csp, orcaEnv, parentRscNameToID, orcaAssetStartTime, orcaAssetEndTime); err != nil {
		return
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.ORCA_ASSET, orcaAssetEndTime)
}

func fetchOrcaAssets(tenantID, csp string, orcaEnv tenant.OrcaEnvironment, parentRscNameToID map[string]string, orcaAssetStartTime, orcaAssetEndTime time.Time) error {

	logger.Print(logger.INFO, "Fetching Orca Alerts for "+csp+" from "+common.DateTime(orcaAssetStartTime)+" to "+common.DateTime(orcaAssetEndTime), []string{tenantID})

	header := make(map[string]string)
	if len(orcaEnv.Token) > 0 {
		header["Authorization"] = "Token " + orcaEnv.Token
	} else {
		return nil
	}

	var (
		nextPageToken   string
		urlParams       = make(map[string]string)
		bulkUpdateQuery string
		currentCount    int
	)

	unsupportedData := NewUnsupportedData()

	filter :=
		`{
			"filter":
			[
				{
					"field": "cloud_provider",
					"includes": "` + csp + `"
				},
				{
					"field": "state.last_seen",
					"range":
					{
						"gt": "` + elastic.DateTime(orcaAssetStartTime) + `",
						"lte": "` + elastic.DateTime(orcaAssetEndTime) + `"
					}
				}
			]
		}`

	for {

		urlParams["dsl_filter"] = filter

		urlParams["limit"] = LIMIT
		if len(nextPageToken) > 0 {
			urlParams["next_page_token"] = nextPageToken
		}

		response, err := transport.SendRequest("GET", ORCA_URL+"query/assets", urlParams, header, nil)
		if err != nil {
			logger.Print(logger.ERROR, "Error Orca API", []string{tenantID}, urlParams, header)
			break
		}

		var assetData map[string]interface{}
		err = json.Unmarshal(response, &assetData)
		if err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			break
		}

		err = processOrcaAssets(assetData, tenantID, csp, orcaAssetEndTime, parentRscNameToID, &bulkUpdateQuery, &currentCount, unsupportedData)
		if err != nil {
			break
		}

		if currentCount > MAX_RECORDS {

			if err := elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkUpdateQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of external cloud resources", []string{tenantID}, err)
				return err
			}

			logger.Print(logger.INFO, "External Cloud Resource bulk API Successful for Orca Assets for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkUpdateQuery = ""
		}

		if hasToken, ok := assetData["has_next_page_token"].(bool); ok && hasToken {
			if token, ok := assetData["next_page_token"].(string); ok {
				nextPageToken = token
			}

			time.Sleep(1 * time.Second)
		} else {
			break
		}
	}

	if currentCount > 0 {

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkUpdateQuery); err != nil {
			logger.Print(logger.ERROR, "Error in bulk insertion of external cloud resources", []string{tenantID}, err)
			return err
		}

		logger.Print(logger.INFO, "External Cloud Resource bulk API Successful for Orca Assets for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
		currentCount = 0
		bulkUpdateQuery = ""
	}

	if len(unsupportedData.AssetTypes) > 0 {
		logger.Print(logger.ERROR, "Unsupported asset types", []string{tenantID}, unsupportedData.AssetTypes)
	}

	if len(unsupportedData.EntityIDNormalize) > 0 {
		logger.Print(logger.ERROR, "Unsupported entity ID normalization", []string{tenantID}, unsupportedData.EntityIDNormalize)
	}

	if len(unsupportedData.EntityJson) > 0 {
		logger.Print(logger.ERROR, "Unsupported entity JSON", []string{tenantID}, unsupportedData.EntityJson)
	}

	return nil
}

func processOrcaAssets(assetData map[string]interface{}, tenantID, csp string, insertionTime time.Time, parentRscNameToID map[string]string, bulkUpdateQuery *string, currentCount *int, unsupportedData *UnsupportedData) error {

	if assetsData, ok := assetData["data"].([]interface{}); ok {
		for _, assetData := range assetsData {

			assetDataBytes, err := json.Marshal(assetData)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling asset data to JSON:", []string{tenantID}, err)
				continue
			}

			var assetsDataMap map[string]interface{}
			err = json.Unmarshal(assetDataBytes, &assetsDataMap)
			if err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON:", []string{tenantID}, err)
				continue
			}

			var precizeAsset commonExtCr.ExternalCloudResource

			skip, err := normalizeOrcaAssetData(assetsDataMap, &precizeAsset, parentRscNameToID, unsupportedData)
			if skip {
				continue
			}

			if err != nil {
				continue
			}

			if len(precizeAsset.EntityID) > 0 {
				precizeAsset.EntityID = strings.ToLower(precizeAsset.EntityID)
			}

			precizeAsset.ID = common.GenerateCombinedHashIDCaseSensitive(tenantID, precizeAsset.AccountID, precizeAsset.EntityType, precizeAsset.EntityID)
			precizeAsset.TenantID = tenantID
			precizeAsset.Source = common.ORCA_SOURCE
			precizeAsset.InsertTime = elastic.DateTime(insertionTime)
			precizeAsset.ServiceID = common.CspStrToIdIntMap[csp]

			precizeAssetBytes, _ := json.Marshal(precizeAsset)

			*bulkUpdateQuery = *bulkUpdateQuery + `{"index": {"_id": "` + precizeAsset.ID + `"}}` + "\n"
			*bulkUpdateQuery += string(precizeAssetBytes) + "\n"

			*currentCount++

		}
	}

	return nil
}
