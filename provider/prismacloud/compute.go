package prismacloud

import (
	"bytes"
	"encoding/json"
	"strconv"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

func GeneratePrismaCloudComputeToken(prismaCloudEnv tenant.PrismaCloudEnvironment, tenantID string) (token string, err error) {

	var authResp []byte

	payload := struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}{
		Username: prismaCloudEnv.Username,
		Password: prismaCloudEnv.Password,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshaling json", []string{tenantID}, err)
		return
	}

	if authResp, err = transport.SendRequest("POST", prismaCloudEnv.URL+"/api/v33.03/authenticate", nil, nil, bytes.NewBuffer(jsonData)); err != nil {
		logger.Print(logger.ERROR, "Error in authenticate api", []string{tenantID}, payload)
		return
	}

	var authRes struct {
		Token string `json:"token"`
	}

	if err = json.Unmarshal(authResp, &authRes); err != nil {
		logger.Print(logger.ERROR, "Error unmarshaling authenticate response", []string{tenantID}, err)
		return
	}

	token = authRes.Token
	return
}

func validateComputeAuth(prismaCloudEnv tenant.PrismaCloudEnvironment, authValidation map[string]bool, tenantID string) (err error) {

	token, err := GeneratePrismaCloudComputeToken(prismaCloudEnv, tenantID)
	if err != nil {
		authValidation[prismaCloudEnv.Username] = false
		return
	}

	header := map[string]string{
		"Accept":         "application/json",
		"x-redlock-auth": token,
	}

	if _, err = transport.SendRequest("GET", prismaCloudEnv.URL+"/api/v33.03/policies/runtime/container", nil, header, nil); err != nil {
		logger.Print(logger.ERROR, "Error in container policy api", []string{tenantID})
		authValidation[prismaCloudEnv.Username] = false
		return
	}

	authValidation[prismaCloudEnv.Username] = true
	return nil
}

func ProcessPrismaComputeAudits(tenantID string, csp string, prismaCloudEnv tenant.PrismaCloudEnvironment, alertStartTime, alertEndTime time.Time) {

	if alertEndTime.Sub(alertStartTime) < bufferTime {
		return
	}

	token, err := GeneratePrismaCloudComputeToken(prismaCloudEnv, tenantID)
	if err != nil {
		return
	}

	header := map[string]string{
		"Accept":         "application/json",
		"x-redlock-auth": token,
	}

	logger.Print(logger.INFO, "Fetching Prisma Compute Alerts from "+common.DateTime(alertStartTime)+" to "+common.DateTime(alertEndTime), []string{tenantID})

	var (
		// nextPageToken   string
		urlParams       = make(map[string]string)
		bulkInsertQuery string
		currentCount    int
	)

	// for {
	urlParams["from"] = strconv.FormatInt(alertStartTime.Unix()*1000, 10)
	urlParams["to"] = strconv.FormatInt(alertEndTime.Unix()*1000, 10)
	urlParams["limit"] = LIMIT
	// if nextPageToken != "" {
	// 	urlParams["pageToken"] = nextPageToken
	// }

	response, err := transport.SendRequest("GET", prismaCloudEnv.URL+"/api/v33.03/audits/runtime/host", urlParams, header, nil)
	if err != nil {
		logger.Print(logger.ERROR, "Error Prisma Compute API", []string{tenantID}, urlParams, header)
		// break
		return
	}

	var auditData []interface{}
	err = json.Unmarshal(response, &auditData)
	if err != nil {
		logger.Print(logger.ERROR, "Error unmarshalling response", []string{tenantID}, err)
		// break
		return
	}

	err = processComputeAudits(auditData, tenantID, csp, alertEndTime, &bulkInsertQuery, &currentCount)
	if err != nil {
		// break
		return
	}

	if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
		logger.Print(logger.ERROR, "Error in bulk insertion", []string{tenantID}, err)
		// break
		return
	}
	logger.Print(logger.INFO, "Bulk API successful for Prisma Compute Alerts", []string{tenantID})

	// if pageToken, ok := auditData["pageToken"].(string); ok {
	// 	nextPageToken = pageToken
	// 	currentCount = 0
	// 	bulkInsertQuery = ""
	// } else {
	// 	break
	// }
	// }

	// if currentCount > 0 {
	// 	if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
	// 		logger.Print(logger.ERROR, "Error in final bulk insertion", []string{tenantID}, err)
	// 	}
	// 	logger.Print(logger.INFO, "Final bulk API successful", []string{tenantID})
	// }

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.PRISMA_ALERT, alertEndTime)

}

// Only Supporting runtime audits for now
// TODO: Other audits, other apis
func processComputeAudits(auditData []interface{}, tenantID, csp string, insertTime time.Time, bulkInsertQuery *string, currentCount *int) error {

	for _, audit := range auditData {

		var prismaAudit common.RuntimeHostAudit
		auditBytes, err := json.Marshal(audit)
		if err != nil {
			return err
		}
		if err := json.Unmarshal(auditBytes, &prismaAudit); err != nil {
			return err
		}

		for _, severity := range prismaAudit.Severity {
			incident := common.Incident{
				ID:       common.GenerateCombinedHashID(tenantID, prismaAudit.ID),
				AlertID:  prismaAudit.ID,
				Issue:    prismaAudit.Label,
				EntityID: prismaAudit.ResourceID,
				// EntityType:    prismaAudit.Resource.ResourceType,
				Source:        "PrismaCloud",
				IssueSeverity: severity,
				// Category:      ,
				CreatedAt: elastic.DateTime(prismaAudit.Time),
				// UpdatedAt:  prismaAudit.LastUpdated,
				// Status:     prismaAudit.Status,
				TenantID:   tenantID,
				InsertTime: elastic.DateTime(insertTime),
			}

			incidentBytes, _ := json.Marshal(incident)
			*bulkInsertQuery += `{"index": {"_id": "` + incident.ID + `"}}\n` + string(incidentBytes) + "\n"
			*currentCount++
		}

	}

	return nil
}
