package gitlab

import (
	"io"
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/xanzy/go-gitlab"
)

func processGitlabJob(gitlabClient *gitlab.Client, job *gitlab.Job, project *gitlab.Project, tenantID, accessToken string, globalError *error, parentRscNameToID map[string]string, missingNormalizeRsc map[string]string) {

	// fetch job by id
	job, _, err := gitlabClient.Jobs.GetJob(project.ID, job.ID)
	if err != nil {
		return
	}

	// fetch job trace
	logFile, _, err := gitlabClient.Jobs.GetTraceFile(project.ID, job.ID)
	if err != nil {
		logger.Print(logger.ERROR, "Got error fetching job trace", []string{tenantID, strconv.Itoa(project.ID), strconv.Itoa(job.ID)}, err)
		return
	}

	bytesData, err := io.ReadAll(logFile)
	if err != nil {
		return
	}

	content := strings.ToLower(string(bytesData))
	if strings.Contains(content, "terraform") || strings.Contains(content, "terragrunt") {

		name := job.User.Name
		if job.User.Name == "" {
			name = job.User.Username
		}
		jobInfo := common.DeplopymentInfo{
			Name:      name,
			Email:     job.User.Email,
			StartTime: elastic.DateTime(*job.CreatedAt),
			EndTime:   elastic.DateTime(*job.FinishedAt),
			Duration:  job.Duration,
			GitClient: "gitlab",
			JobID:     strconv.Itoa(job.ID),
		}
		common.ProcessTraceFile(content, tenantID, jobInfo, parentRscNameToID, missingNormalizeRsc)
	}

}
