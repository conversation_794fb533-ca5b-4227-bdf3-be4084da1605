package gitlab

import (
	"bytes"
	"encoding/json"
	"path"
	"strconv"
	"time"

	"github.com/xanzy/go-gitlab"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const GITLAB_DOMAIN = "https://gitlab.example.com"

type GitlabTFStateVersionsDoc struct {
	StateVersionID string                 `json:"stateVersionId"`
	Project        string                 `json:"project"`
	CommitPath     string                 `json:"commitPath"`
	CreatedBy      string                 `json:"createdBy"`
	CreatedAt      string                 `json:"createdAt"`
	TenantID       string                 `json:"tenantId"`
	ResourceEvents []common.ResourceEvent `json:"resourceEvent"`
}

type GraphQLStateResponse struct {
	Data struct {
		Project struct {
			TerraformStates struct {
				Nodes []struct {
					CreatedAt     time.Time `json:"createdAt"`
					Name          string    `json:"name"`
					LatestVersion struct {
						ID  string `json:"id"`
						Job struct {
							CommitPath string `json:"commitPath"`
							Status     string `json:"status"`
						} `json:"job"`
						DownloadPath  string    `json:"downloadPath"`
						CreatedAt     time.Time `json:"createdAt"`
						CreatedByUser struct {
							Name string `json:"name"`
						} `json:"createdByUser"`
					} `json:"latestVersion"`
				} `json:"nodes"`
			} `json:"terraformStates"`
		} `json:"project"`
	} `json:"data"`
}

func processGitlabTerraformStates(gitlabEnv tenant.GitlabEnvironment, project *gitlab.Project, tenantID string, stateStartTime, stateEndTime time.Time) (err error) {

	projectIDString := strconv.Itoa(project.ID)

	domain := GITLAB_DOMAIN

	graphQlQueryGetState := `{"query": "query {project(fullPath: \"` + project.PathWithNamespace + `\") {terraformStates {nodes {createdAt name latestVersion{id job {commitPath status} downloadPath createdAt createdByUser {name}}}}}}"}`

	stateResp, err := transport.SendRequest(
		"POST",
		domain+"/api/graphql",
		nil,
		map[string]string{"Authorization": "Bearer " + gitlabEnv.Token},
		bytes.NewBuffer([]byte(graphQlQueryGetState)),
	)
	if err != nil {
		return
	}

	var graphqlStateResp GraphQLStateResponse

	if err = json.Unmarshal(stateResp, &graphqlStateResp); err != nil {
		logger.Print(logger.ERROR, "Got error calling unmarshal", []string{tenantID, projectIDString}, err)
		return
	}

	if stateNodes := graphqlStateResp.Data.Project.TerraformStates.Nodes; len(stateNodes) > 0 {

		for _, state := range stateNodes {

			if state.LatestVersion.CreatedAt.After(stateStartTime) && state.LatestVersion.CreatedAt.Before(stateEndTime) {

				if state.LatestVersion.Job.Status == "FAILED" {
					continue
				}

				latestStateVersionContent, err := transport.SendRequest(
					"GET",
					domain+state.LatestVersion.DownloadPath,
					nil,
					map[string]string{"Authorization": "Bearer " + gitlabEnv.Token},
					nil,
				)
				if err != nil {
					continue
				}

				var stateFile common.TerraformStateFile

				if err = json.Unmarshal(latestStateVersionContent, &stateFile); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshaling state", []string{tenantID, projectIDString}, err)
					continue
				}

				var (
					stateAuthor = state.LatestVersion.CreatedByUser.Name
					action      = "modified"
					commitPath  string
				)

				if commitPath = state.LatestVersion.Job.CommitPath; len(commitPath) > 0 {

					commitID := path.Base(commitPath)

					gitlabCommitAuthorQuery := `{"_source":["author","fileStatus"],"query":{"bool":{"filter":[{"match":{"commitId.keyword":"` + commitID + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"gitClient.keyword":"gitlab"}}]}},"size":1}`

					gitlabCommitDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.IAC_GIT_COMMITS_INDEX}, gitlabCommitAuthorQuery)
					if err != nil {
						continue
					}

					if len(gitlabCommitDocs) == 1 {

						for _, gitlabCommitDoc := range gitlabCommitDocs {

							if author, ok := gitlabCommitDoc["author"].(string); ok {
								stateAuthor = author
							}

							if fileStatus, ok := gitlabCommitDoc["fileStatus"].(string); ok {
								action = fileStatus
							}
						}
					}
				}

				var resourceEvents []common.ResourceEvent

				for _, resource := range stateFile.Resources {

					if resource.Mode == common.RESOURCE_MODE_MANAGED {

						for _, instance := range resource.Instances {

							resourceEvent := common.GetResourceMetadataFromTerraform(instance.Attributes, resource.Type, resource.Provider)
							resourceEvents = append(resourceEvents, resourceEvent)

							if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
								ResourceEvent: resourceEvent,
								Action:        action,
								TenantID:      tenantID,
								User:          stateAuthor,
								UserType:      "gitlab",
							}); err != nil {
								continue
							}
						}
					}
				}

				if _, err = elastic.InsertDocument(tenantID, elastic.GITLAB_TFSTATEVERSIONS_INDEX, GitlabTFStateVersionsDoc{
					StateVersionID: state.LatestVersion.ID,
					Project:        project.Name,
					CommitPath:     commitPath,
					CreatedBy:      stateAuthor,
					CreatedAt:      elastic.DateTime(state.LatestVersion.CreatedAt),
					TenantID:       tenantID,
					ResourceEvents: resourceEvents,
				}); err != nil {
					continue
				}
			}
		}
	}

	return
}
