package gitlab

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/xanzy/go-gitlab"

	"github.com/precize/common"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

func ValidateAuth(gitlabEnv tenant.GitlabEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	authValidation = make(map[string]bool)

	accessToken, err := getGitlabAccessToken(tenantID, gitlabEnv.Token)
	if err != nil {
		return
	}

	gitlabClient, err := gitlab.NewOAuthClient(accessToken)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating gitlab client", []string{tenantID}, err)
		return
	}

	if _, _, err = gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
		Membership: gitlab.Bool(true),
		ListOptions: gitlab.ListOptions{
			Page:    1,
			PerPage: 1,
		},
	}); err != nil {
		logger.Print(logger.ERROR, "Got error getting projects", []string{tenantID}, err)
		authValidation[accessToken] = false
		return
	}

	authValidation[accessToken] = true
	return
}

func ProcessGitlabData(tenantID string, gitlabEnv tenant.GitlabEnvironment, gitlabCommitStartTime, gitlabEndTime, tenantStartTime time.Time, preCommitCron bool) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.GITLAB_COMMIT, tenantStartTime)

	accessToken, err := getGitlabAccessToken(tenantID, gitlabEnv.Token)
	if err != nil {
		return
	}

	gitlabClient, err := gitlab.NewOAuthClient(accessToken)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating gitlab client", []string{tenantID}, err)
		return
	}

	if defaultTime.Equal(gitlabCommitStartTime) {
		onboardGitlab(tenantID, accessToken, gitlabClient)
		logger.Print(logger.INFO, "Done Gitlab Scanning")
	}

	if !preCommitCron && (gitlabEndTime.Sub(gitlabCommitStartTime)) > gitlabEndTime.Sub(defaultTime) {
		gitlabCommitStartTime = defaultTime
	}

	logger.Print(logger.INFO, "Fetching gitlab files from "+common.DateTime(gitlabCommitStartTime)+" to "+common.DateTime(gitlabEndTime), []string{tenantID})

	projectsPage := 1
	var globalErr error

	for {

		projectMap := make(map[string]struct{})

		for _, r := range gitlabEnv.Projects {
			projectMap[r] = struct{}{}
		}

		projects, projectsResp, err := gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
			Membership: gitlab.Bool(true),
			ListOptions: gitlab.ListOptions{
				Page:    projectsPage,
				PerPage: 90,
			},
		})
		if err != nil {
			if strings.Contains(err.Error(), "401") {
				gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
				projects, projectsResp, err = gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
					Membership: gitlab.Bool(true),
					ListOptions: gitlab.ListOptions{
						Page:    projectsPage,
						PerPage: 90,
					},
				})
				if err != nil {
					globalErr = err
					logger.Print(logger.ERROR, "Got error getting projects", []string{tenantID}, err)
					return
				}
			}
		}

		for _, project := range projects {

			if _, ok := projectMap[project.Name]; !ok {
				if _, ok := projectMap[project.PathWithNamespace]; !ok {
					continue
				}
			}

			commitsPage := 1

			for {

				commits, commitsResp, err := gitlabClient.Commits.ListCommits(project.ID, &gitlab.ListCommitsOptions{
					All:   gitlab.Bool(true),
					Since: &gitlabCommitStartTime,
					Until: &gitlabEndTime,
					ListOptions: gitlab.ListOptions{
						Page:    commitsPage,
						PerPage: 90,
					},
				})
				if err != nil {
					if strings.Contains(err.Error(), "401") {
						gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
						commits, commitsResp, err = gitlabClient.Commits.ListCommits(project.ID, &gitlab.ListCommitsOptions{
							All:   gitlab.Bool(true),
							Since: &gitlabCommitStartTime,
							Until: &gitlabEndTime,
							ListOptions: gitlab.ListOptions{
								Page:    commitsPage,
								PerPage: 90,
							},
						})
						if err != nil {
							globalErr = err
							logger.Print(logger.ERROR, "Got error listing commits", []string{tenantID, strconv.Itoa(project.ID)}, err)
							break
						}
					}
				}

				for _, commit := range commits {

					if preCommitCron && common.CommitCollected(tenantID, commit.ID) {
						continue
					}

					processGitlabCommit(gitlabClient, commit, project, tenantID, accessToken, preCommitCron, &globalErr)
				}

				if commitsResp.NextPage == 0 {
					break
				}

				gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)

				commitsPage = commitsResp.NextPage
			}
		}

		if projectsResp.NextPage == 0 {
			break
		}

		projectsPage = projectsResp.NextPage
	}

	if globalErr == nil {
		tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.GITLAB_COMMIT, gitlabEndTime)
	}

}

func getGitlabAccessToken(tenantID, token string) (string, error) {

	type GitLabToken struct {
		AccessToken  string `json:"access_token"`
		TokenType    string `json:"token_type"`
		Scope        string `json:"scope"`
		RefreshToken string `json:"refresh_token"`
	}

	var gitLabToken GitLabToken

	if err := json.Unmarshal([]byte(token), &gitLabToken); err != nil {
		logger.Print(logger.ERROR, "Error while unmarshalling gitlab token", tenantID, err)
		return "", err
	}

	return gitLabToken.AccessToken, nil
}

func refreshGitlabToken(tenantID string, accessToken *string, expiredGitlabClient *gitlab.Client) *gitlab.Client {

	tenantData, err := tenant.GetTenantData(tenantID, false)
	if err != nil {
		logger.Print(logger.ERROR, "Error: ", err)
		return expiredGitlabClient
	}

	for _, gitlabEnv := range tenantData.GitlabAccounts.Environment {
		accessTokn, err := getGitlabAccessToken(tenantID, gitlabEnv.Token)
		if err != nil {
			return expiredGitlabClient
		}
		*accessToken = accessTokn

		gitlabClient, err := gitlab.NewOAuthClient(accessTokn)
		if err != nil {
			logger.Print(logger.ERROR, "Got error creating gitlab client", []string{tenantID}, err)
			return expiredGitlabClient
		} else {
			return gitlabClient
		}
	}
	return expiredGitlabClient
}
