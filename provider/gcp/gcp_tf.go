package gcp

import (
	"bytes"
	"encoding/json"
	"path/filepath"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

type GCPResourcesRequest struct {
	Parent string `json:"parent"`
	Query  string `json:"query"`
}

type GCPResourcesResponse struct {
	Labels map[string]string `json:"labels"`
}

func ProcessGCPTerraformEvents(tenantID, envID, projectID string, eventStartTime, eventEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.GCP_TF_RESOURCE, tenantStartTime)

	if (eventEndTime.Sub(eventStartTime)) > eventEndTime.Sub(defaultTime) {
		eventStartTime = defaultTime
	}

	startTime := elastic.DateTime(eventStartTime)
	endTime := elastic.DateTime(eventEndTime)

	searchQuery := `{"query":{"bool":{"filter":[{"match":{"sourceApp.keyword":"Terraform"}},{"range":{"eventTime":{"gt":"` + startTime + `","lte":"` + endTime + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + projectID + `"}}]}}}`

	tfEventDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_ACTIVITY_INDEX}, searchQuery)
	if err != nil {
		return
	}

	if len(tfEventDocs) > 0 {
		logger.Print(logger.INFO, "Fetched gcp terraform events from "+startTime+" to "+endTime, []string{tenantID, projectID})
	}

	resourceIdToEventDocMap := make(map[string]common.ResourceEventCopy)
	resourceTypeToIDsMap := make(map[string][]string)
	uniqueResourceIds := make(map[string]struct{})
	lastEventTime := eventStartTime

	var terraformResourceDocs []common.TerraformResourceDoc

	for _, tfEventDoc := range tfEventDocs {

		if resourceInterfaces, ok := tfEventDoc["resources"].([]interface{}); ok {
			for _, resourceInterface := range resourceInterfaces {

				if resourceMap, ok := resourceInterface.(map[string]interface{}); ok {

					if resourceType, ok := resourceMap["resourceType"].(string); ok {

						resourceIDs := make([]string, 0)

						if resourceID, ok := resourceMap["resourceName"].(string); ok {
							if strings.Contains(resourceID, "[") && strings.Contains(resourceID, "]") {

								trimmedID := strings.Trim(resourceID, "[]")
								trimmedIDs := strings.Split(trimmedID, ",")

								resourceIDs = append(resourceIDs, trimmedIDs...)

							} else {
								resourceIDs = append(resourceIDs, resourceID)
							}
						}

						for _, resourceID := range resourceIDs {

							if cloudTrailEvent, ok := tfEventDoc["cloudTrailEvent"].(string); ok {

								if eventName, ok := tfEventDoc["eventName"].(string); ok {

									if serviceCode, ok := tfEventDoc["serviceCode"].(string); ok {

										if eventTimeStr, ok := tfEventDoc["eventTime"].(string); ok {

											if accountID, ok := tfEventDoc["accountId"].(string); ok {

												if region, ok := tfEventDoc["region"].(string); ok {

													eventTime, err := elastic.ParseDateTime(eventTimeStr)
													if err != nil {
														logger.Print(logger.ERROR, "Error parse date time", eventTime)
														return
													}
													if eventTime.After(lastEventTime) {
														lastEventTime = eventTime
													}

													if resourceType == "" || resourceID == "" {
														continue
													}

													eventResourceId := resourceID

													if strings.Contains(strings.ToLower(eventName), "delete") {
														//TODO: Fetch From DB
														continue
													} else {

														var cloudTrailEventMap map[string]interface{}

														err = json.Unmarshal([]byte(cloudTrailEvent), &cloudTrailEventMap)
														if err != nil {
															logger.Print(logger.ERROR, "Error unmarshalling JSON", cloudTrailEvent, err)
															return
														}

														if (!strings.Contains(resourceID, "region") && region != "") || (!strings.Contains(resourceID, "lcoation") && region != "") {
															resourceID = "region/" + region + "/" + resourceID
														}
														if !strings.Contains(resourceID, "project") && accountID != "" {
															resourceID = "accountId/" + accountID + "/" + resourceID
														}

														if _, ok := uniqueResourceIds[resourceID]; !ok {
															uniqueResourceIds[resourceID] = struct{}{}
															if resourceIDs, ok := resourceTypeToIDsMap[resourceType]; !ok {
																ids := make([]string, 0)
																ids = append(ids, resourceID)
																resourceTypeToIDsMap[resourceType] = ids

															} else {
																resourceIDs = append(resourceIDs, resourceID)
																resourceTypeToIDsMap[resourceType] = resourceIDs
															}

															resourceIdToEventDocMap[resourceID] = common.ResourceEventCopy{
																ResourceMap:     cloudTrailEventMap,
																ResourceType:    resourceType,
																ResourceName:    resourceID,
																ServiceCode:     serviceCode,
																EventName:       eventName,
																EventTime:       eventTime,
																Region:          region,
																EventResourceID: eventResourceId,
															}
														}
													}

													var precizeTfTagged bool

													var terraformResourceDoc = common.TerraformResourceDoc{
														Account: projectID,
														CSP:     common.GCP_SERVICE_CODE,
													}

													var (
														buf               bytes.Buffer
														gcpResourcesResp  GCPResourcesResponse
														gcpResourcesUrl   = `/precize/private/gcp/getResourcesByFilter/` + envID
														gcpResourceParent = "projects/" + projectID
														gcpResourcesQuery = "name:" + filepath.Base(resourceID)

														gcpResourcesRequest = GCPResourcesRequest{
															Parent: gcpResourceParent,
															Query:  gcpResourcesQuery,
														}
													)

													err = json.NewEncoder(&buf).Encode(gcpResourcesRequest)
													if err != nil {
														logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID, projectID}, err)
														continue
													}

													getGCPResources, err := transport.SendRequestToServer("POST", gcpResourcesUrl, nil, &buf)
													if err != nil {
														continue
													}

													if len(getGCPResources) > 0 {

														if err = json.Unmarshal(getGCPResources, &gcpResourcesResp); err != nil {
															logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, projectID}, err)
															continue
														}

														for key, value := range gcpResourcesResp.Labels {

															switch key {
															case "precize_git_commit":
																precizeTfTagged = true
																terraformResourceDoc.CommitID = value
															case "precize_git_repo":
																terraformResourceDoc.Repository = value
															case "precize_git_file":
																terraformResourceDoc.Filename = value
															}
														}

														if !precizeTfTagged {
															continue
														}

														terraformResourceDoc.EventTime = eventTimeStr

														terraformResourceDoc.Approach = "yor"

														eventTime, err := time.Parse(elastic.DATE_FORMAT, eventTimeStr)
														if err != nil {
															logger.Print(logger.ERROR, "Got error parsing event time", []string{tenantID, projectID}, err)
															// return
														}

														if eventTime.After(lastEventTime) {
															lastEventTime = eventTime
														}

														terraformResourceDocs = append(terraformResourceDocs, terraformResourceDoc)
													}
												}
											}

										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	lastEventTimeTf, err := common.MapEventToTfResources(resourceIdToEventDocMap, resourceTypeToIDsMap, common.GCP_SERVICE_CODE, tenantID, projectID, eventStartTime)
	if err != nil {
		return
	}

	if !lastEventTimeTf.IsZero() && lastEventTimeTf.After(eventEndTime) {
		lastEventTime = lastEventTimeTf
	}

	for _, terraformResourceDoc := range terraformResourceDocs {

		if _, err = elastic.InsertDocument(tenantID, elastic.TERRAFORM_RESOURCES_INDEX, terraformResourceDoc); err != nil {
			continue
		}

		gitUsers, err := common.FetchGitFileAuthorsForTerraform(tenantID, terraformResourceDoc.CommitID,
			terraformResourceDoc.Filename, terraformResourceDoc.ResourceID, common.GCP_SERVICE_CODE)
		if err != nil {
			logger.Print(logger.ERROR, "Got error fetching git file authors", []string{tenantID, terraformResourceDoc.Account}, err)
			continue
		}

		for _, gitUser := range gitUsers {

			resourceEvent := common.ResourceEvent{
				ResourceID:   terraformResourceDoc.ResourceID,
				ResourceName: terraformResourceDoc.ResourceID,
				Region:       terraformResourceDoc.Region,
				Account:      terraformResourceDoc.Account,
			}

			if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
				ResourceEvent: resourceEvent,
				Action:        gitUser.Action,
				TenantID:      tenantID,
				User:          gitUser.Name,
				UserType:      gitUser.Client,
				EventTime:     gitUser.CommitTime,
				DocID:         gitUser.DocID,
			}); err != nil {
				continue
			}
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.GCP_TF_RESOURCE, lastEventTime)
}
