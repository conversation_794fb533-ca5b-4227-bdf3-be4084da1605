package aws

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/cloudformation"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func processStackEvents(stackTemplateDoc StackTemplateDoc, gitUsers []common.GitUser, cloudFormationClient *cloudformation.Client) {

	var (
		describeStackEventsNextToken *string
		stackID                      = &stackTemplateDoc.StackID
		resourceEvents               []common.ResourceEvent
		stackCompletionWaitCounter   int
	)

	for {

		describeStacksInput := cloudformation.DescribeStacksInput{
			StackName: stackID,
		}

		describeStacks, err := cloudFormationClient.DescribeStacks(context.TODO(), &describeStacksInput, func(o *cloudformation.Options) {
			o.Region = stackTemplateDoc.Region
		})
		if err != nil {
			logger.Print(logger.ERROR, "Got error calling DescribeStacks", []string{
				stackTemplateDoc.TenantID, stackTemplateDoc.Account}, err)
			break
		}

		if len(describeStacks.Stacks) > 0 {

			if strings.HasSuffix(string(describeStacks.Stacks[0].StackStatus), "IN_PROGRESS") {
				time.Sleep(30 * time.Second)
				stackCompletionWaitCounter++
				if stackCompletionWaitCounter > 360 {
					logger.Print(logger.INFO, "Waited too long for stack to complete. Timing out", []string{
						stackTemplateDoc.TenantID, stackTemplateDoc.Account}, stackTemplateDoc.StackID)
					return
				}

				continue
			}

			break
		}
	}

	for {

		describeStackEventsInput := cloudformation.DescribeStackEventsInput{
			StackName: stackID,
			NextToken: describeStackEventsNextToken,
		}

		stackEvents, err := cloudFormationClient.DescribeStackEvents(context.TODO(), &describeStackEventsInput, func(o *cloudformation.Options) {
			o.Region = stackTemplateDoc.Region
		})
		if err != nil {
			logger.Print(logger.ERROR, "Got error calling DescribeStackEvents", []string{
				stackTemplateDoc.TenantID, stackTemplateDoc.Account}, err)
			break
		}

		for _, stackEvent := range stackEvents.StackEvents {
			// Ignore in progress events; Ignore events of the stack itself
			if strings.HasSuffix(string(stackEvent.ResourceStatus), "COMPLETE") && (aws.ToString(stackID) != aws.ToString(stackEvent.PhysicalResourceId)) {

				stackTemplateEventTime, err := time.Parse(elastic.DATE_FORMAT, stackTemplateDoc.EventTime)
				if err != nil {
					logger.Print(logger.ERROR, "Got error parsing event time", []string{
						stackTemplateDoc.TenantID, stackTemplateDoc.Account}, err)
				}

				// To get only new events during stack updates
				if stackEvent.Timestamp.After(stackTemplateEventTime) {

					resourceEvent := common.ResourceEvent{
						ResourceID:   aws.ToString(stackEvent.PhysicalResourceId),
						ResourceName: aws.ToString(stackEvent.LogicalResourceId),
						ResourceType: aws.ToString(stackEvent.ResourceType),
					}

					resourceEvents = append(resourceEvents, resourceEvent)

					resourceEvent.Region = stackTemplateDoc.Region
					resourceEvent.Account = stackTemplateDoc.Account

					for _, gitUser := range gitUsers {

						gitCommitSearchQuery := `{
							"query": {
								"bool": {
									"filter": [
									{"match": {"_id": "` + gitUser.DocID + `"}}
									]
								}
								}
							}`

						commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, gitCommitSearchQuery)
						if err != nil {
							continue
						}

						resourceEventPriorityConfig := make(map[string]interface{})
						resourceEventPriorityConfigJsonDataStr := ""

						if len(commitDocs) <= 0 {
							continue
						}

						if priorityConfigsJsonStr, ok := commitDocs[0]["priorityConfigs"].(string); ok {
							var priorityConfigsMap map[string]interface{}

							if priorityConfigsJsonStr == "" {
								continue
							}

							err := json.Unmarshal([]byte(priorityConfigsJsonStr), &priorityConfigsMap)
							if err != nil {
								logger.Print(logger.ERROR, "Error Unmarshaling priority configs json", err)
								continue
							}

							if len(priorityConfigsMap) == 0 {
								continue
							}

							for key, val := range priorityConfigsMap {
								if strings.Contains(key, resourceEvent.ResourceName) {
									if valMap, ok := val.(map[string]interface{}); ok {
										resourceEventPriorityConfig = valMap
										break
									}
								}
							}
						}

						if len(resourceEventPriorityConfig) != 0 {
							resourceEventPriorityConfigJsonData, err := json.Marshal(resourceEventPriorityConfig)
							if err != nil {
								logger.Print(logger.ERROR, "Got error marshalling static properties", err)
								continue
							}
							resourceEventPriorityConfigJsonDataStr = string(resourceEventPriorityConfigJsonData)
						}

						if _, err = elastic.InsertDocument(stackTemplateDoc.TenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
							ResourceEvent:   resourceEvent,
							Action:          gitUser.Action,
							TenantID:        stackTemplateDoc.TenantID,
							User:            gitUser.Name,
							UserType:        gitUser.Client,
							EventTime:       gitUser.CommitTime,
							DocID:           gitUser.DocID,
							PriorityConfigs: resourceEventPriorityConfigJsonDataStr,
						}); err != nil {
							continue
						}
					}
				}
			}
		}

		describeStackEventsNextToken = stackEvents.NextToken

		if describeStackEventsNextToken == nil {
			break
		}

	}

	stackTemplateDoc.ResourceEvents = resourceEvents

	if _, err := elastic.InsertDocument(stackTemplateDoc.TenantID, elastic.CFSTACK_TEMPLATES_INDEX, stackTemplateDoc); err != nil {
		return
	}
}
