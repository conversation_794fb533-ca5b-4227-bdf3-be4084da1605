package aws

import (
	"bytes"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	bufferTime = 24 * time.Hour
)

type secHubFindingsRequest struct {
	AccountID string             `json:"accountId"`
	Region    string             `json:"region"`
	NextToken string             `json:"nextToken"`
	Filters   securityHubFilters `json:"filters"`
}

type securityHubFilters struct {
	UpdatedAt   []rangeFilter `json:"updatedAt"`
	RecordState []valueFilter `json:"recordState"`
	IssueLabel  []valueFilter `json:"severityLabel"`
}

type valueFilter struct {
	Value      string `json:"value"`
	Comparison string `json:"comparison"`
}

type rangeFilter struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type secHubFindingsResp struct {
	Status int        `json:"status"`
	Data   secHubData `json:"data"`
}

type secHubData struct {
	Findings  []secHubFinding `json:"findings"`
	NextToken string          `json:"nextToken"`
}

type secHubFinding struct {
	ID              string   `json:"id"`
	Region          string   `json:"region"`
	AWSAccountID    string   `json:"awsAccountId"`
	Types           []string `json:"types"`
	FirstObservedAt string   `json:"firstObservedAt"`
	LastObservedAt  string   `json:"lastObservedAt"`
	CreatedAt       string   `json:"createdAt"`
	UpdatedAt       string   `json:"updatedAt"`
	Severity        struct {
		Label string `json:"label"`
	} `json:"severity"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Workflow    struct {
		Status string `json:"status"`
	} `json:"workflow"`
	Remediation struct {
		Recommendation struct {
			Text string `json:"text"`
			URL  string `json:"url"`
		} `json:"recommendation"`
	} `json:"remediation"`
	SourceUrl   string           `json:"sourceUrl"`
	Resources   []secHubResource `json:"resources"`
	RecordState string           `json:"recordState"`

	//	Vulnerabilities
	//  Criticality
	//	Compliance
	//	Malware
	//	Threats
}

type secHubResource struct {
	Type string `json:"type"`
	ID   string `json:"id"`
}

func ProcessSecurityHubEvents(tenantID, envID string, awsAccountDetails tenant.AWSAccountDetails, secHubStartTime, secHubEndTime, tenantStartTime time.Time) {

	var (
		nextToken          string
		firstEverIteration bool
	)

	if secHubEndTime.Sub(secHubStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.SECHUB_FINDINGS, tenantStartTime)

	if (secHubEndTime.Sub(secHubStartTime)) >= secHubEndTime.Sub(defaultTime) {
		secHubStartTime = defaultTime
		firstEverIteration = true
	}

	logger.Print(logger.INFO, "Fetching Security hub findings from "+common.DateTime(secHubStartTime)+" to "+common.DateTime(secHubEndTime), []string{tenantID, awsAccountDetails.AccountID})

	for _, region := range common.AWS_REGIONS {

		for {

			awsSecHubUrl := `/precize/private/aws/securityHubFindings/tenant/` + tenantID + `/environment/` + envID

			secHubReq := secHubFindingsRequest{
				AccountID: awsAccountDetails.AccountID,
				Region:    region,
				Filters: securityHubFilters{
					UpdatedAt: []rangeFilter{
						{
							Start: elastic.DateTime(secHubStartTime),
							End:   elastic.DateTime(secHubEndTime),
						},
					},
					IssueLabel: []valueFilter{
						{
							Comparison: "NOT_EQUALS",
							Value:      "INFORMATIONAL",
						},
						{
							Comparison: "NOT_EQUALS",
							Value:      "LOW",
						},
					},
				},
				NextToken: nextToken,
			}

			if firstEverIteration {
				secHubReq.Filters.RecordState = []valueFilter{
					{
						Comparison: "NOT_EQUALS",
						Value:      "ARCHIVED",
					},
				}
			}

			var (
				secHubFindingsBuf bytes.Buffer
				secHubResp        secHubFindingsResp
			)

			err := json.NewEncoder(&secHubFindingsBuf).Encode(secHubReq)
			if err != nil {
				logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
				break
			}

			resp, err := transport.SendRequestToServer("POST", awsSecHubUrl, nil, &secHubFindingsBuf)
			if err != nil {
				break
			}

			if err = json.Unmarshal(resp, &secHubResp); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				break
			}

			if err := insertSecHubEvents(secHubResp.Data.Findings, tenantID, secHubEndTime, firstEverIteration); err != nil {
				break
			}

			if len(secHubResp.Data.NextToken) > 0 {
				nextToken = secHubResp.Data.NextToken
			} else {
				break
			}
		}
	}

	firstEverIteration = false

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.SECHUB_FINDINGS, secHubEndTime)
}

func insertSecHubEvents(secHubFindings []secHubFinding, tenantID string, insertTime time.Time, firstEverIteration bool) error {

	var (
		bulkInsertQuery string
		currentCount    int
		maxRecords      = 10000
	)

	for _, finding := range secHubFindings {

		if firstEverIteration && finding.Workflow.Status == "RESOLVED" {
			continue
		}

		createdAt, err := time.Parse(time.RFC3339, finding.CreatedAt)
		if err != nil {
			logger.Print(logger.ERROR, "Got error parsing date", []string{tenantID}, err)
			continue
		}

		updatedAt, err := time.Parse(time.RFC3339, finding.UpdatedAt)
		if err != nil {
			logger.Print(logger.ERROR, "Got error parsing date", []string{tenantID}, err)
			continue
		}

		jsonBytes, err := json.Marshal(finding)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		var (
			serviceID          = common.AWS_SERVICE_ID_INT
			sourceRisk, status string
			additionalData     = map[string]interface{}{}
		)

		additionalDataBytes, err := json.Marshal(additionalData)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		if sourceRisk = finding.Severity.Label; len(sourceRisk) > 0 {
			sourceRisk = common.ConvertToTitleCase(sourceRisk)
		} else {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		if _, ok := common.ValidRiskLevel[sourceRisk]; !ok {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		status = common.INCIDENT_STATUS_OPEN
		if finding.RecordState == "ARCHIVED" || finding.Workflow.Status == "RESOLVED" {
			status = common.INCIDENT_STATUS_RESOLVED
		}

		incidentDocID := common.GenerateCombinedHashID(tenantID, finding.ID)

		incident := common.Incident{
			ID:             incidentDocID,
			AlertID:        finding.ID,
			Issue:          finding.Title,
			AccountID:      finding.AWSAccountID,
			Source:         common.SECURITYHUB_SOURCE,
			IssueSeverity:  sourceRisk,
			SourceRisk:     sourceRisk,
			CreatedAt:      elastic.DateTime(createdAt),
			UpdatedAt:      elastic.DateTime(updatedAt),
			ServiceID:      serviceID,
			Category:       strings.Join(finding.Types, ", "),
			Description:    finding.Description,
			Status:         status,
			Stage:          "dc",
			TenantID:       tenantID,
			SourceJson:     string(jsonBytes),
			InsertTime:     elastic.DateTime(insertTime),
			AdditionalData: string(additionalDataBytes),
			IsIncident:     false,
		}

		incident.EntityID, incident.EntityType = getEntityIDAndTypeFromSecurityHubType(finding, tenantID)

		if len(incident.EntityType) <= 0 {
			continue
		}

		incident.CrsID = common.GenerateCombinedHashIDCaseSensitive(tenantID, strconv.Itoa(serviceID), incident.AccountID, incident.EntityID, incident.EntityType)

		crsDoc, err := elastic.GetDocument(elastic.CLOUD_RESOURCE_STORE_INDEX, incident.CrsID)
		if err != nil {
			continue
		}

		if len(crsDoc) > 0 {

			b, err := json.Marshal(crsDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
				continue
			}

			var crs common.CloudResourceStoreDoc

			if err = json.Unmarshal(b, &crs); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				continue
			}

			incident.Environment = crs.Environment
			incident.Owner = crs.Owner
			incident.ResourceName = crs.ResourceName
			incident.AccountName = crs.AccountName
			incident.RelatedResources = crs.RelatedResources
		}

		incidentBytes, _ := json.Marshal(incident)
		bulkInsertQuery = bulkInsertQuery + `{"index": {"_id": "` + incidentDocID + `"}}` + "\n"
		bulkInsertQuery += string(incidentBytes) + "\n"

		currentCount++

		if currentCount > maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Cloud Incident bulk API Successful for AWS SecurityHub Findings for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkInsertQuery = ""
		}
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Cloud Incident bulk API Successful for AWS SecurityHub Findings for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	return nil
}

func getEntityIDAndTypeFromSecurityHubType(finding secHubFinding, tenantID string) (entityID, entityType string) {

	var (
		secHubResourceID, secHubResourceType string
		entityIDChanged                      bool
	)

	if len(finding.Resources) > 0 {
		secHubResourceID = finding.Resources[0].ID
		secHubResourceType = finding.Resources[0].Type
	} else {
		return
	}

	switch secHubResourceType {
	case "AwsAccount":
		entityType = common.AWS_ACCOUNT_RESOURCE_TYPE
		idSplit := strings.Split(secHubResourceID, ":")
		if len(idSplit) > 0 {
			entityID = strings.ToLower(idSplit[len(idSplit)-1])
			entityIDChanged = true
		}
	case "AwsEc2Instance", "AwsSsmAssociationCompliance", "AwsSsmPatchCompliance":
		entityType = common.AWS_EC2_RESOURCE_TYPE
	case "AwsEc2Subnet":
		entityType = common.AWS_SUBNET_RESOURCE_TYPE
	case "AwsEc2Volume":
		entityType = common.AWS_EBSVOLUME_RESOURCE_TYPE
	case "AwsIamAccessKey":
		entityType = common.AWS_IAM_USER_RESOURCE_TYPE
	case "AwsEcrContainerImage":
		entityType = common.AWS_CONTAINERIMAGE_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsLambdaFunction":
		entityType = common.AWS_LAMBDA_RESOURCE_TYPE
		idSplit := strings.Split(secHubResourceID, ":")
		if len(idSplit) > 1 {
			entityID = strings.ToLower(idSplit[len(idSplit)-2])
			entityIDChanged = true
		}
	case "Other":
		return

	default:
		logger.Print(logger.INFO, "Unsupported type for AWS Security Hub", []string{tenantID}, secHubResourceType)
		return
	}

	if !entityIDChanged {
		idSplit := strings.Split(secHubResourceID, "/")
		if len(idSplit) > 0 {
			entityID = strings.ToLower(idSplit[len(idSplit)-1])
		}
	}

	return
}
