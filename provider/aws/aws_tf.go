package aws

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/sts"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

type TfCloudtrailEvent struct {
	RequestParameters struct {
		TagSpecificationSet struct {
			Items []struct {
				Tags []struct {
					Key   string `json:"key"`
					Value string `json:"value"`
				} `json:"tags"`
			} `json:"items"`
		} `json:"tagSpecificationSet,omitempty"`
		TagSet struct {
			Items []struct {
				Key   string `json:"key"`
				Value string `json:"value"`
			} `json:"items"`
		} `json:"tagSet,omitempty"`
		Tagging struct {
			TagSet struct {
				Tag interface{} `json:"Tag"`
			} `json:"TagSet,omitempty"`
		} `json:"Tagging,omitempty"`
		// Ignore
		TaggingString string `json:"tagging,omitempty"`
	} `json:"requestParameters"`
}

func ProcessAWSTerraformEvents(tenantID, externalID, accessKey, secretToken string, awsAccount tenant.AWSAccountDetails, eventStartTime, eventEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.AWS_TF_RESOURCE, tenantStartTime)

	if (eventEndTime.Sub(eventStartTime)) > eventEndTime.Sub(defaultTime) {
		eventStartTime = defaultTime
	}

	lastEventTime := eventStartTime
	basicCreds := aws.NewCredentialsCache(
		credentials.NewStaticCredentialsProvider(
			accessKey,
			secretToken,
			"",
		),
	)

	cfg, err := config.LoadDefaultConfig(
		context.TODO(),
		config.WithRegion(DEFAULT_REGION),
		config.WithCredentialsProvider(basicCreds),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error loading assumer config", []string{tenantID}, err)
		return
	}

	if len(awsAccount.AssumedRole) > 0 {

		// assume role using the assumer config above
		stsclient := sts.NewFromConfig(cfg)
		assumedCreds := stscreds.NewAssumeRoleProvider(
			stsclient,
			awsAccount.AssumedRole,
			func(o *stscreds.AssumeRoleOptions) {
				o.ExternalID = aws.String(externalID)
			},
		)

		cfg.Credentials = aws.NewCredentialsCache(assumedCreds)
	}

	accountID := awsAccount.AccountID

	startTime := elastic.DateTime(eventStartTime)
	endTime := elastic.DateTime(eventEndTime)

	searchQuery := `{"query":{"bool":{"filter":[{"match":{"sourceApp.keyword":"Terraform"}},{"range":{"eventTime":{"gt":"` + startTime + `","lte":"` + endTime + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + accountID + `"}}]}}}`

	tfEventDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_ACTIVITY_INDEX}, searchQuery)
	if err != nil {
		return
	}

	if len(tfEventDocs) > 0 {
		logger.Print(logger.INFO, "Fetched aws terraform events from "+startTime+" to "+endTime, []string{tenantID, accountID})
	}

	resourceIdToEventDocMap := make(map[string]common.ResourceEventCopy)
	resourceTypeToIDsMap := make(map[string][]string)
	uniqueResourceIds := make(map[string]struct{})
	var terraformResourceDocs []common.TerraformResourceDoc

	for _, tfEventDoc := range tfEventDocs {

		if resourceInterfaces, ok := tfEventDoc["resources"].([]interface{}); ok {
			for _, resourceInterface := range resourceInterfaces {

				if resourceMap, ok := resourceInterface.(map[string]interface{}); ok {

					if resourceType, ok := resourceMap["resourceType"].(string); ok {

						resourceIDs := make([]string, 0)

						if resourceID, ok := resourceMap["resourceName"].(string); ok {
							if strings.Contains(resourceID, "[") && strings.Contains(resourceID, "]") {

								trimmedID := strings.Trim(resourceID, "[]")
								trimmedIDs := strings.Split(trimmedID, ",")

								resourceIDs = append(resourceIDs, trimmedIDs...)

							} else {
								resourceIDs = append(resourceIDs, resourceID)
							}
						}

						for _, resourceID := range resourceIDs {

							if cloudTrailEvent, ok := tfEventDoc["cloudTrailEvent"].(string); ok {

								if eventName, ok := tfEventDoc["eventName"].(string); ok {

									if serviceCode, ok := tfEventDoc["serviceCode"].(string); ok {

										if eventTimeStr, ok := tfEventDoc["eventTime"].(string); ok {

											if accountID, ok := tfEventDoc["accountId"].(string); ok {

												if region, ok := tfEventDoc["region"].(string); ok {

													eventTime, err := elastic.ParseDateTime(eventTimeStr)
													if err != nil {
														logger.Print(logger.ERROR, "Error parse date time", eventTime)
														return
													}

													if eventTime.After(lastEventTime) {
														lastEventTime = eventTime
													}

													if resourceType == "" || resourceID == "" {
														continue
													}

													if strings.Contains(strings.ToLower(eventName), "delete") {
														//TODO: Fetch From DB
														continue
													} else {

														var cloudTrailEventMap map[string]interface{}

														err = json.Unmarshal([]byte(cloudTrailEvent), &cloudTrailEventMap)
														if err != nil {
															logger.Print(logger.ERROR, "Error unmarshalling JSON", cloudTrailEvent, err)
															return
														}

														eventResourceId := resourceID

														if !strings.Contains(resourceID, "region") && region != "" {
															resourceID = "region/" + region + "/" + resourceID
														}
														if !strings.Contains(resourceID, "accountId") && accountID != "" {
															resourceID = "accountId/" + accountID + "/" + resourceID
														}

														if _, ok := uniqueResourceIds[resourceID]; !ok {
															uniqueResourceIds[resourceID] = struct{}{}
															if resourceIDs, ok := resourceTypeToIDsMap[resourceType]; !ok {
																ids := make([]string, 0)
																ids = append(ids, resourceID)
																resourceTypeToIDsMap[resourceType] = ids

															} else {
																resourceIDs = append(resourceIDs, resourceID)
																resourceTypeToIDsMap[resourceType] = resourceIDs
															}

															resourceIdToEventDocMap[resourceID] = common.ResourceEventCopy{
																ResourceMap:     cloudTrailEventMap,
																ResourceType:    resourceType,
																ResourceName:    resourceID,
																ServiceCode:     serviceCode,
																EventName:       eventName,
																EventTime:       eventTime,
																Region:          region,
																EventResourceID: eventResourceId,
															}
														}
													}

													var precizeTfTagged bool

													var terraformResourceDoc = common.TerraformResourceDoc{
														Account: accountID,
														CSP:     common.AWS_SERVICE_CODE,
													}

													var cloudtrailEventType TfCloudtrailEvent

													err = json.Unmarshal([]byte(cloudTrailEvent), &cloudtrailEventType)
													if err != nil {
														logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, accountID}, err, cloudTrailEvent)
														continue
													}

													for _, v := range cloudtrailEventType.RequestParameters.TagSet.Items {

														switch v.Key {
														case "precize_git_commit":
															precizeTfTagged = true
															terraformResourceDoc.CommitID = v.Value
														case "precize_git_repo":
															terraformResourceDoc.Repository = v.Value
														case "precize_git_file":
															terraformResourceDoc.Filename = v.Value
														}
													}

													for _, v := range cloudtrailEventType.RequestParameters.TagSpecificationSet.Items {

														for _, tag := range v.Tags {

															switch tag.Key {
															case "precize_git_commit":
																precizeTfTagged = true
																terraformResourceDoc.CommitID = tag.Value
															case "precize_git_repo":
																terraformResourceDoc.Repository = tag.Value
															case "precize_git_file":
																terraformResourceDoc.Filename = tag.Value
															}
														}
													}

													if tagInterfaceSlice, ok := cloudtrailEventType.RequestParameters.Tagging.TagSet.Tag.([]interface{}); ok {

														for _, tagInterface := range tagInterfaceSlice {

															if tag, ok := tagInterface.(map[string]interface{}); ok {

																switch tag["Key"].(string) {
																case "precize_git_commit":
																	precizeTfTagged = true
																	terraformResourceDoc.CommitID = tag["Value"].(string)
																case "precize_git_repo":
																	terraformResourceDoc.Repository = tag["Value"].(string)
																case "precize_git_file":
																	terraformResourceDoc.Filename = tag["Value"].(string)
																}
															}
														}
													} else if tag, ok := cloudtrailEventType.RequestParameters.Tagging.TagSet.Tag.(map[string]interface{}); ok {

														switch tag["Key"].(string) {
														case "precize_git_commit":
															precizeTfTagged = true
															terraformResourceDoc.CommitID = tag["Value"].(string)
														case "precize_git_repo":
															terraformResourceDoc.Repository = tag["Value"].(string)
														case "precize_git_file":
															terraformResourceDoc.Filename = tag["Value"].(string)
														}
													}

													if !precizeTfTagged {
														continue
													}

													terraformResourceDoc.ResourceID = resourceID
													terraformResourceDoc.ResourceName = resourceID
													terraformResourceDoc.Region = region
													terraformResourceDoc.EventTime = eventTimeStr
													terraformResourceDoc.TenantID = tenantID
													terraformResourceDoc.Approach = "yor"

													terraformResourceDocs = append(terraformResourceDocs, terraformResourceDoc)
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	lastEventTimeTf, err := common.MapEventToTfResources(resourceIdToEventDocMap, resourceTypeToIDsMap, common.AWS_SERVICE_CODE, tenantID, accountID, eventStartTime)
	if err != nil {
		return
	}

	if !lastEventTimeTf.IsZero() && lastEventTimeTf.After(eventEndTime) {
		lastEventTime = lastEventTimeTf
	}

	for _, terraformResourceDoc := range terraformResourceDocs {

		if _, err = elastic.InsertDocument(tenantID, elastic.TERRAFORM_RESOURCES_INDEX, terraformResourceDoc); err != nil {
			continue
		}

		gitUsers, err := common.FetchGitFileAuthorsForTerraform(tenantID, terraformResourceDoc.CommitID,
			terraformResourceDoc.Filename, terraformResourceDoc.ResourceID, common.AWS_SERVICE_CODE)
		if err != nil {
			logger.Print(logger.ERROR, "Got error fetching git file authors", []string{tenantID, terraformResourceDoc.Account}, err)
			continue
		}

		for _, gitUser := range gitUsers {

			resourceEvent := common.ResourceEvent{
				ResourceID:   terraformResourceDoc.ResourceID,
				ResourceName: terraformResourceDoc.ResourceID,
				Region:       terraformResourceDoc.Region,
				Account:      terraformResourceDoc.Account,
			}

			if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
				ResourceEvent: resourceEvent,
				Action:        gitUser.Action,
				TenantID:      tenantID,
				User:          gitUser.Name,
				UserType:      gitUser.Client,
				EventTime:     gitUser.CommitTime,
				DocID:         gitUser.DocID,
			}); err != nil {
				continue
			}
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.AWS_TF_RESOURCE, lastEventTime)
}
