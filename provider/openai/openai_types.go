package openai

const (
	PRECIZE_SERVICEACCOUNT_PREFIX = "Precize Service Account - "
	ORGANIZATION_NAME             = "OpenAI-Organization"
)

type ProjectsResponse struct {
	Data    []Project `json:"data"`
	LastID  string    `json:"last_id"`
	Has<PERSON><PERSON> bool      `json:"has_more"`
}

type Project struct {
	ID                     string `json:"id"`
	Name                   string `json:"name"`
	CreatedAt              int64  `json:"created_at"`
	ArchivedAt             int64  `json:"archived_at"`
	Status                 string `json:"status"`
	IsModelBlockingEnabled bool   `json:"isModelBlockingEnabled"`
}

type ProjectUsersResponse struct {
	Data    []ProjectUser `json:"data"`
	LastID  string        `json:"last_id"`
	HasMore bool          `json:"has_more"`
}

type ProjectUser struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Email   string `json:"email"`
	Role    string `json:"role"`
	AddedAt int64  `json:"added_at"`
}

type ProjectServiceAccountsResponse struct {
	Data    []ProjectServiceAccount `json:"data"`
	LastID  string                  `json:"last_id"`
	Has<PERSON>ore bool                    `json:"has_more"`
}

type ProjectServiceAccount struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Role      string `json:"role"`
	CreatedAt int64  `json:"created_at"`
}

type ProjectAPIKeysResponse struct {
	Data    []ProjectAPIKey `json:"data"`
	LastID  string          `json:"last_id"`
	HasMore bool            `json:"has_more"`
}

type ProjectAPIKey struct {
	ID            string `json:"id"`
	Name          string `json:"name"`
	RedactedValue string `json:"redacted_value"`
	CreatedAt     int64  `json:"created_at"`
	Owner         struct {
		Type           string                `json:"type"`
		User           ProjectUser           `json:"user"`
		ServiceAccount ProjectServiceAccount `json:"service_account"`
	} `json:"owner"`
}

type AdminAPIKeysResponse struct {
	Data    []AdminAPIKey `json:"data"`
	LastID  string        `json:"last_id"`
	HasMore bool          `json:"has_more"`
}

type AdminAPIKey struct {
	Object        string  `json:"object"`
	ID            string  `json:"id"`
	Name          string  `json:"name"`
	CreatedAt     int64   `json:"created_at"`
	LastUsedAt    int64   `json:"last_used_at"`
	Owner         Owner   `json:"owner"`
	RedactedValue string  `json:"redacted_value"`
	Value         *string `json:"value,omitempty"`
}

type Owner struct {
	Object    string `json:"object"`
	ID        string `json:"id"`
	Name      string `json:"name"`
	Role      string `json:"role"`
	Type      string `json:"type"`
	CreatedAt int64  `json:"created_at"`
}

type ProjectRateLimitResponse struct {
	Data    []ProjectRateLimit `json:"data"`
	LastID  string             `json:"last_id"`
	HasMore bool               `json:"has_more"`
}

type ProjectRateLimit struct {
	ID                      string `json:"id"`
	Model                   string `json:"model"`
	MaxRequestsPerMinute    int    `json:"max_requests_per_1_minute"`
	MaxTokensPerMinute      int    `json:"max_tokens_per_1_minute"`
	MaxImagesPerMinute      int    `json:"max_images_per_1_minute,omitempty"`
	MaxRequestsPerDay       int    `json:"max_requests_per_1_day,omitempty"`
	Batch1DayMaxInputTokens int    `json:"batch_1_day_max_input_tokens,omitempty"`
	MaxAudioMegabytesPerMin int    `json:"max_audio_megabytes_per_1_minute,omitempty"`
}

type UsersResponse struct {
	Data    []User `json:"data"`
	LastID  string `json:"last_id"`
	HasMore bool   `json:"has_more"`
}

type User struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Email   string `json:"email"`
	Role    string `json:"role"`
	AddedAt int64  `json:"added_at"`
}

type OpenAIBatchResponse struct {
	Data    []Batch `json:"data"`
	LastID  string  `json:"last_id"`
	FirstID string  `json:"first_id"`
	HasMore bool    `json:"has_more"`
}

type Batch struct {
	ID               string            `json:"id"`
	Object           string            `json:"object"`
	Endpoint         string            `json:"endpoint"`
	Errors           ErrorList         `json:"errors"`
	InputFileID      string            `json:"input_file_id"`
	CompletionWindow string            `json:"completion_window"`
	Status           string            `json:"status"`
	OutputFileID     string            `json:"output_file_id"`
	ErrorFileID      string            `json:"error_file_id"`
	CreatedAt        int64             `json:"created_at"`
	InProgressAt     int64             `json:"in_progress_at"`
	ExpiresAt        int64             `json:"expires_at"`
	FinalizingAt     int64             `json:"finalizing_at"`
	CompletedAt      int64             `json:"completed_at"`
	FailedAt         int64             `json:"failed_at"`
	ExpiredAt        int64             `json:"expired_at"`
	CancellingAt     int64             `json:"cancelling_at"`
	CancelledAt      int64             `json:"cancelled_at"`
	RequestCounts    RequestCounts     `json:"request_counts"`
	Metadata         map[string]string `json:"metadata"`
}

type ErrorList struct {
	Object string  `json:"object"` // Always "list"
	Data   []Error `json:"data"`
}

type Error struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Param       string `json:"param,omitempty"`
	Line        int    `json:"line,omitempty"`
	InputFileID string `json:"input_file_id"`
}

type RequestCounts struct {
	Total     int `json:"total"`
	Completed int `json:"completed"`
	Failed    int `json:"failed"`
}

type OpenAIAssistantResponse struct {
	Data    []Assistant `json:"data"`
	LastID  string      `json:"last_id"`
	FirstID string      `json:"first_id"`
	HasMore bool        `json:"has_more"`
}

type Assistant struct {
	ID            string            `json:"id"`
	Object        string            `json:"object"`
	CreatedAt     int64             `json:"created_at"`
	Name          string            `json:"name,omitempty"`
	Description   string            `json:"description,omitempty"`
	Model         string            `json:"model"`
	Instructions  string            `json:"instructions,omitempty"`
	Tools         []Tool            `json:"tools"`
	ToolResources ToolResources     `json:"tool_resources,omitempty"`
	Metadata      map[string]string `json:"metadata"`
	Temperature   float64           `json:"temperature,omitempty"`
	TopP          float64           `json:"top_p,omitempty"`
}

type Tool struct {
	Type string `json:"type"`
}

type ToolResources struct {
	FileIDs        []string `json:"file_ids,omitempty"`
	VectorStoreIDs []string `json:"vector_store_ids,omitempty"`
}

type OpenAIModelResponse struct {
	Data    []OpenAIModel `json:"data"`
	LastID  string        `json:"last_id"`
	FirstID string        `json:"first_id"`
	HasMore bool          `json:"has_more"`
}

type OpenAIModel struct {
	ID                          string   `json:"id"`
	Created                     int64    `json:"created"`
	Object                      string   `json:"object"`
	OwnedBy                     string   `json:"owned_by"`
	Description                 string   `json:"description,omitempty"`
	UseCase                     string   `json:"user_case,omitempty"`
	ModelType                   string   `json:"model_type,omitempty"`
	Stage                       string   `json:"stage,omitempty"`
	DeploymentDate              int64    `json:"deployment_date,omitempty"`
	TrainingFile                string   `json:"training_file,omitempty"`
	BaseModel                   string   `json:"base_model,omitempty"`
	HasPii                      bool     `json:"has_pii"`
	HasPci                      bool     `json:"has_pci"`
	HasPhi                      bool     `json:"has_phi"`
	Used                        bool     `json:"used"`
	ResultFiles                 []string `json:"result_files,omitempty"`
	ValidationFile              string   `json:"validation_file,omitempty"`
	Assistants                  []string `json:"assistants,omitempty"`
	TrainingInstability         bool     `json:"training_instability,omitempty"`
	TrainingAccuracyFluctuation bool     `json:"training_accuracy_fluctuation,omitempty"`
	MaxRequestsPerMinute        int      `json:"max_requests_per_1_minute,omitempty"`
	MaxTokensPerMinute          int      `json:"max_tokens_per_1_minute,omitempty"`
	MaxImagesPerMinute          int      `json:"max_images_per_1_minute,omitempty"`
	MaxRequestsPerDay           int      `json:"max_requests_per_1_day,omitempty"`
	Batch1DayMaxInputTokens     int      `json:"batch_1_day_max_input_tokens,omitempty"`
	MaxAudioMegabytesPerMin     int      `json:"max_audio_megabytes_per_1_minute,omitempty"`
}

type OpenAIFineTuningJobResponse struct {
	Data    []FineTuningJob `json:"data"`
	LastID  string          `json:"last_id"`
	FirstID string          `json:"first_id"`
	HasMore bool            `json:"has_more"`
}

type FineTuningJob struct {
	ID               string          `json:"id"`
	CreatedAt        int64           `json:"created_at"`
	Error            ErrorDetails    `json:"error,omitempty"`
	FineTunedModel   string          `json:"fine_tuned_model,omitempty"`
	FinishedAt       int64           `json:"finished_at,omitempty"`
	Hyperparameters  Hyperparameters `json:"hyperparameters"`
	Model            string          `json:"model"`
	Object           string          `json:"object"`
	OrganizationID   string          `json:"organization_id"`
	ResultFiles      []string        `json:"result_files"`
	Status           string          `json:"status"`
	TrainedTokens    int64           `json:"trained_tokens,omitempty"`
	TrainingFile     string          `json:"training_file"`
	ValidationFile   string          `json:"validation_file,omitempty"`
	Integrations     []Integration   `json:"integrations,omitempty"`
	EstimatedFinish  int64           `json:"estimated_finish,omitempty"`
	CheckpointModels []string        `json:"checkpoint_models,omitempty"`
}

type ErrorDetails struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type Hyperparameters struct {
	NEpochs interface{} `json:"n_epochs"`
}

type Integration struct {
	Type  string            `json:"type"`
	WandB *WandBIntegration `json:"wandb,omitempty"`
}

type WandBIntegration struct {
	Project string   `json:"project"`
	Name    string   `json:"name,omitempty"`
	Entity  string   `json:"entity,omitempty"`
	Tags    []string `json:"tags"`
	Seed    int      `json:"seed"`
}

type OpenAIFineTuningJobEventResponse struct {
	Data    []FineTuningJobEvent `json:"data"`
	LastID  string               `json:"last_id"`
	FirstID string               `json:"first_id"`
	HasMore bool                 `json:"has_more"`
}

type FineTuningJobEvent struct {
	ID        string `json:"id"`
	CreatedAt int64  `json:"created_at"`
	Level     string `json:"level"`
	Message   string `json:"message"`
	Object    string `json:"object"`
}

type OpenAIFineTuningJobCheckpointResponse struct {
	Data    []FineTuningJobCheckpoint `json:"data"`
	LastID  string                    `json:"last_id"`
	FirstID string                    `json:"first_id"`
	HasMore bool                      `json:"has_more"`
}

type FineTuningJobCheckpoint struct {
	ID                       string                 `json:"id"`
	CreatedAt                int64                  `json:"created_at"`
	FineTunedModelCheckpoint string                 `json:"fine_tuned_model_checkpoint"`
	StepNumber               int                    `json:"step_number"`
	Metrics                  map[string]interface{} `json:"metrics"`
	FineTuningJobID          string                 `json:"fine_tuning_job_id"`
	Object                   string                 `json:"object"`
}

type OpenAIFileResponse struct {
	Data    []OpenAIFile `json:"data"`
	LastID  string       `json:"last_id"`
	FirstID string       `json:"first_id"`
	HasMore bool         `json:"has_more"`
}

type OpenAIFile struct {
	ID                string `json:"id"`
	CreatedAt         int64  `json:"created_at"`
	Object            string `json:"object"`
	Bytes             int64  `json:"bytes"`
	FileName          string `json:"filename"`
	Purpose           string `json:"purpose"`
	Type              string `json:"file_type"`
	HasPii            bool   `json:"has_pii,omitempty"`
	HasPci            bool   `json:"has_pci,omitempty"`
	HasPhi            bool   `json:"has_phi,omitempty"`
	SensitivityStatus string `json:"sensitivity_status"`
}

type CreateServiceAccountRequest struct {
	Name string `json:"name"`
}

type CreateServiceAccountResponse struct {
	Name   string `json:"name"`
	ID     string `json:"id"`
	Object string `json:"object"`
	Role   string `json:"role"`
	APIKey struct {
		Value string `json:"value"`
	} `json:"api_key"`
}

type MessageReq struct {
	Role        string       `json:"role"`
	Content     string       `json:"content"`
	Attachments []Attachment `json:"attachments,omitempty"`
}

type Attachment struct {
	FileID string `json:"file_id"`
	Tools  []Tool `json:"tools"`
}

type ThreadRequest struct {
	AssistantID string `json:"assistant_id"`
	Thread      Thread `json:"thread"`
}

type Thread struct {
	Messages []MessageReq `json:"messages"`
}

type ThreadResponse struct {
	ID            string              `json:"id"`
	ThreadID      string              `json:"thread_id"`
	Object        string              `json:"object"`
	CreatedAt     int64               `json:"created_at"`
	ToolResources ThreadToolResources `json:"tool_resources"`
	Metadata      map[string]string   `json:"metadata"`
}

type ThreadToolResources struct {
	CodeInterpreter CodeInterpreterResources `json:"code_interpreter,omitempty"`
	FileSearch      FileSearchResources      `json:"file_search,omitempty"`
}

type CodeInterpreterResources struct {
	FileIDs []string `json:"file_ids"`
}

type FileSearchResources struct {
	VectorStoreIDs []string `json:"vector_store_ids"`
}

type MessageResponse struct {
	Data    []Message `json:"data"`
	LastID  string    `json:"last_id"`
	HasMore bool      `json:"has_more"`
}

type Message struct {
	ID           string            `json:"id"`
	Object       string            `json:"object"`
	CreatedAt    int64             `json:"created_at"`
	ThreadID     string            `json:"thread_id"`
	Status       string            `json:"status"`
	CompletedAt  int64             `json:"completed_at,omitempty"`
	IncompleteAt int64             `json:"incomplete_at,omitempty"`
	Role         string            `json:"role"`
	Content      []Content         `json:"content"`
	AssistantID  string            `json:"assistant_id,omitempty"`
	RunID        string            `json:"run_id,omitempty"`
	Attachments  []Attachment      `json:"attachments,omitempty"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

type Content struct {
	Type    string  `json:"type"`
	Text    Text    `json:"text,omitempty"`
	Refusal Refusal `json:"refusal,omitempty"`
}

type Text struct {
	Value string `json:"value"`
}

type Refusal struct {
	Content string `json:"content"`
}

type AuditLogsResponse struct {
	Data    []interface{} `json:"data"`
	LastID  string        `json:"last_id"`
	FirstID string        `json:"first_id"`
	HasMore bool          `json:"has_more"`
}

type FineTuningResultFile struct {
	Step                   int     `csv:"step"`
	TrainLoss              float64 `csv:"train_loss"`
	TrainAccuracy          float64 `csv:"train_accuracy"`
	ValidLoss              float64 `csv:"valid_loss"`
	ValidMeanTokenAccuracy float64 `csv:"valid_mean_token_accuracy"`
}

type CostAmount struct {
	Currency string  `json:"currency"`
	Value    float64 `json:"value"`
}

type GroupedCost struct {
	Object    string     `json:"object"`
	Amount    CostAmount `json:"amount"`
	LineItem  *string    `json:"line_item"`
	ProjectID *string    `json:"project_id"`
	StartTime int64      `json:"start_time"`
	EndTime   int64      `json:"end_time"`
}

type CostBucket struct {
	Object    string        `json:"object"`
	StartTime int64         `json:"start_time"`
	EndTime   int64         `json:"end_time"`
	Results   []GroupedCost `json:"results"`
}

type CostPage struct {
	Object   string       `json:"object"`
	Data     []CostBucket `json:"data"`
	HasMore  bool         `json:"has_more"`
	NextPage *string      `json:"next_page"`
}

type CompletionsUsageResp struct {
	Object   string                 `json:"object"`
	Data     []CompletionsUsageData `json:"data"`
	HasMore  bool                   `json:"has_more"`
	NextPage *string                `json:"next_page"`
}

type CompletionsUsageData struct {
	Object    string             `json:"object"`
	StartTime int64              `json:"start_time"`
	EndTime   int64              `json:"end_time"`
	Results   []CompletionsUsage `json:"results"`
}

type CompletionsUsage struct {
	Object            string  `json:"object"`
	APIKeyID          *string `json:"api_key_id"`
	Batch             *bool   `json:"batch"`
	InputAudioTokens  int     `json:"input_audio_tokens"`
	InputCachedTokens int     `json:"input_cached_tokens"`
	InputTokens       int     `json:"input_tokens"`
	Model             *string `json:"model"`
	NumModelRequests  int     `json:"num_model_requests"`
	OutputAudioTokens int     `json:"output_audio_tokens"`
	OutputTokens      int     `json:"output_tokens"`
	ProjectID         *string `json:"project_id"`
	UserID            *string `json:"user_id"`
	StartTime         int64   `json:"start_time"`
	EndTime           int64   `json:"end_time"`
}

type ExpirationPolicy struct {
	Anchor string `json:"anchor"`
	Days   int    `json:"days"`
}

type FileCounts struct {
	Cancelled  int `json:"cancelled"`
	Completed  int `json:"completed"`
	Failed     int `json:"failed"`
	InProgress int `json:"in_progress"`
	Total      int `json:"total"`
}

type VectorStore struct {
	Object       string                 `json:"object"`
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Status       string                 `json:"status"`
	CreatedAt    int64                  `json:"created_at"`
	LastActiveAt *int64                 `json:"last_active_at"`
	ExpiresAt    *int64                 `json:"expires_at"`
	ExpiresAfter ExpirationPolicy       `json:"expires_after"`
	FileCounts   FileCounts             `json:"file_counts"`
	UsageBytes   int64                  `json:"usage_bytes"`
	Metadata     map[string]interface{} `json:"metadata"`
}

type OpenAIVectorStoreResponse struct {
	Data    []VectorStore `json:"data"`
	LastID  string        `json:"last_id"`
	FirstID string        `json:"first_id"`
	HasMore bool          `json:"has_more"`
}

type VectorStoreFile struct {
	Object        string                 `json:"object"`
	ID            string                 `json:"id"`
	VectorStoreID string                 `json:"vector_store_id"`
	Status        string                 `json:"status"`
	CreatedAt     int64                  `json:"created_at"`
	UsageBytes    int64                  `json:"usage_bytes"`
	LastError     *VectorStoreFileError  `json:"last_error,omitempty"`
	Attributes    map[string]interface{} `json:"attributes,omitempty"`
	Name          string                 `json:"name"`
	Bytes         int64                  `json:"bytes"`
}

type VectorStoreFileError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type OpenAIVectorStoreFileResponse struct {
	Data    []VectorStoreFile `json:"data"`
	LastID  string            `json:"last_id"`
	FirstID string            `json:"first_id"`
	HasMore bool              `json:"has_more"`
}

type OpenAIEvent struct {
	Name   string `json:"name"`
	Region string `json:"region"`
	Time   string `json:"time"`

	EpochTime int64 `json:"-"`
}

type EventsAggregationResponse struct {
	ByUsername struct {
		Buckets []struct {
			Key         string `json:"key"`
			LatestEvent struct {
				Hits struct {
					Hits []struct {
						Source struct {
							EventTime string `json:"eventTime"`
							EventName string `json:"eventName"`
							Region    string `json:"region"`
						} `json:"_source"`
					} `json:"hits"`
				} `json:"hits"`
			} `json:"latest_event"`
		} `json:"buckets"`
	} `json:"by_username"`
}
