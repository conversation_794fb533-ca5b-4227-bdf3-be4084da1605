package openai

import "time"

type OpenAIMetaData struct {
	TenantID            string
	ProjectID           string
	AdminKey            string
	SaKey               string
	CollectedAt         int64
	StaticModelMetaData map[string]interface{}
	AIMetaDataMap       AIMetaData
	DefaultProjectID    string
	startTime           time.Time
	endTime             time.Time
	OrgID               string
}

type AIMetaData struct {
	ModelMetaData         map[string]ModelMetaData
	FineTuningJobMetaData map[string]FineTuningJobMetaData
	FileMetaData          map[string]FileMetaData
	Assistants            []string
}

type ModelMetaData struct {
	ModelID                     string
	TrainingFile                string
	Description                 string
	Stage                       string
	ModelType                   string
	DeploymentDate              int64
	HasPii                      bool
	HasPci                      bool
	HasPhi                      bool
	BaseModel                   string
	ResultFiles                 []string
	Used                        bool
	ValidationFile              string
	Assistants                  []string
	TrainingInstability         bool
	TrainingAccuracyFluctuation bool
	MaxRequestsPerMinute        int `json:"max_requests_per_1_minute,omitempty"`
	MaxTokensPerMinute          int `json:"max_tokens_per_1_minute,omitempty"`
	MaxImagesPerMinute          int `json:"max_images_per_1_minute,omitempty"`
	MaxRequestsPerDay           int `json:"max_requests_per_1_day,omitempty"`
	Batch1DayMaxInputTokens     int `json:"batch_1_day_max_input_tokens,omitempty"`
	MaxAudioMegabytesPerMin     int `json:"max_audio_megabytes_per_1_minute,omitempty"`
}

type FineTuningJobMetaData struct {
	TrainingFile     string
	ModelID          string
	BaseModel        string
	CheckpointModels []string
	ValidationFile   string
}

type FileMetaData struct {
	Type   string
	HasPii bool
	HasPci bool
	HasPhi bool
	Name   string
	Bytes  int64
}
