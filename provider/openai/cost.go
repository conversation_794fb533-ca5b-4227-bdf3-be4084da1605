package openai

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type modelPricingStruct struct {
	Input       float64 `json:"input"`
	CachedInput float64 `json:"cached_input"`
	Output      float64 `json:"output"`
}

func collectProjectCost(aiMetaData *OpenAIMetaData, openAIStartTime, openAIEndTime time.Time) {

	var (
		nextPage            string
		headers             = map[string]string{"Authorization": "Bearer " + aiMetaData.AdminKey}
		usersAIResourceDocs []common.AIResourcesDoc
	)

	for {

		if openAIStartTime.IsZero() {
			openAIStartTime = openAIEndTime.AddDate(0, -1, 0)
		}

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit":        "30",
			"start_time":   strconv.FormatInt(openAIStartTime.Unix(), 10),
			"end_time":     strconv.FormatInt(openAIEndTime.Unix(), 10),
			"bucket_width": "1d",
			"group_by":     "project_id",
		}

		if len(nextPage) > 0 {
			queryParams["page"] = nextPage
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/costs", queryParams, headers, nil)
		if err != nil {
			return
		}

		var costsResponse CostPage

		if err = json.Unmarshal(resp, &costsResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{}, err)
			break
		}

		for _, costResults := range costsResponse.Data {

			for _, cost := range costResults.Results {

				cost.StartTime = costResults.StartTime
				cost.EndTime = costResults.EndTime

				entityJson, err := json.Marshal(cost)
				if err != nil {
					continue
				}

				if cost.ProjectID == nil {
					continue
				}

				aiResourcesDoc := common.AIResourcesDoc{
					EntityID:       *cost.ProjectID,
					EntityType:     common.OPENAI_PROJECTCOST_RESOURCE_TYPE,
					AccountID:      aiMetaData.OrgID,
					StageCompleted: "dc",
					CollectedAt:    aiMetaData.CollectedAt,
					ServiceID:      common.OPENAI_SERVICE_ID_INT,
					TenantID:       aiMetaData.TenantID,
					EntityJson:     string(entityJson),
					Deleted:        false,
				}

				aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID+fmt.Sprint(costResults.StartTime)+fmt.Sprint(costResults.EndTime), aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
				usersAIResourceDocs = append(usersAIResourceDocs, aiResourcesDoc)
			}
		}

		if costsResponse.HasMore {
			nextPage = *costsResponse.NextPage
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_PROJECTCOST_RESOURCE_TYPE, aiMetaData.OrgID, usersAIResourceDocs); err != nil {
		return
	}

	return
}

func collectOrgCost(aiMetaData *OpenAIMetaData, openAIStartTime, openAIEndTime time.Time) {

	var (
		nextPage            string
		headers             = map[string]string{"Authorization": "Bearer " + aiMetaData.AdminKey}
		usersAIResourceDocs []common.AIResourcesDoc
	)

	for {

		if openAIStartTime.IsZero() {
			openAIStartTime = openAIEndTime.AddDate(0, -1, 0)
		}

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit":        "30",
			"start_time":   strconv.FormatInt(openAIStartTime.Unix(), 10),
			"end_time":     strconv.FormatInt(openAIEndTime.Unix(), 10),
			"bucket_width": "1d",
		}

		if len(nextPage) > 0 {
			queryParams["page"] = nextPage
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/costs", queryParams, headers, nil)
		if err != nil {
			return
		}

		var costsResponse CostPage

		if err = json.Unmarshal(resp, &costsResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{}, err)
			break
		}

		for _, costResults := range costsResponse.Data {

			for _, cost := range costResults.Results {

				entityJson, err := json.Marshal(cost)
				if err != nil {
					continue
				}

				cost.StartTime = costResults.StartTime
				cost.EndTime = costResults.EndTime

				aiResourcesDoc := common.AIResourcesDoc{
					EntityID:       aiMetaData.OrgID,
					EntityType:     common.OPENAI_ORGCOST_RESOURCE_TYPE,
					AccountID:      aiMetaData.OrgID,
					StageCompleted: "dc",
					CollectedAt:    aiMetaData.CollectedAt,
					ServiceID:      common.OPENAI_SERVICE_ID_INT,
					TenantID:       aiMetaData.TenantID,
					EntityJson:     string(entityJson),
					Deleted:        false,
				}

				aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID+fmt.Sprint(costResults.StartTime)+fmt.Sprint(costResults.EndTime), aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
				usersAIResourceDocs = append(usersAIResourceDocs, aiResourcesDoc)
			}
		}

		if costsResponse.HasMore {
			nextPage = *costsResponse.NextPage
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_ORGCOST_RESOURCE_TYPE, aiMetaData.OrgID, usersAIResourceDocs); err != nil {
		return
	}

	return
}

func calculateModelCost(aiMetaData *OpenAIMetaData) {
	projectQuery := `{"query":{"bool":{"must":[{"wildcard":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAIProject"}},{"match":{"deleted":"false"}}]}}}`
	projDocs, err := elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, projectQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
		return
	}

	for _, projDoc := range projDocs {
		if projID, ok := projDoc["entityId"].(string); ok {

			modelsTokenCount := make(map[string]map[string]int)

			// accumulate project cost
			projCostQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + aiMetaData.TenantID + `"}},{"match":{"entityType.keyword":"openAICompletionUsage"}},{"match":{"accountId.keyword":"` + projID + `"}},{"match":{"deleted":"false"}}]}}}`
			aiRscDocs, err := elastic.ExecuteSearchQuery([]string{elastic.AI_RESOURCES_INDEX}, projCostQuery)
			if err != nil {
				logger.Print(logger.ERROR, "Got error fetching project", []string{aiMetaData.TenantID}, err)
				return
			}

			for _, aiRscDoc := range aiRscDocs {

				if entityJsonStr, ok := aiRscDoc["entityJson"].(string); ok {

					var completionSruct CompletionsUsage
					json.Unmarshal([]byte(entityJsonStr), &completionSruct)

					if completionSruct.Model == nil {
						continue
					}

					if _, ok := modelsTokenCount[*completionSruct.Model]; !ok {
						modelsTokenCount[*completionSruct.Model] = make(map[string]int)
					}

					modelsTokenCount[*completionSruct.Model]["input"] += completionSruct.InputTokens
					modelsTokenCount[*completionSruct.Model]["cached_input"] += completionSruct.InputCachedTokens
					modelsTokenCount[*completionSruct.Model]["output"] += completionSruct.OutputTokens
				}

			}

			for model, tokenCount := range modelsTokenCount {
				cost, err := CalculateCost(strings.ToLower(model), tokenCount["input"], tokenCount["cached_input"], tokenCount["output"])
				if err != nil && !strings.Contains(strings.ToLower(model), "ft:") {
					logger.Print(logger.INFO, "Got error calculating cost", []string{aiMetaData.TenantID})
					continue
				}

				// update model cost in json
				modelDocID := common.GenerateCombinedHashID(model, common.OPENAI_MODEL_RESOURCE_TYPE, projID, aiMetaData.TenantID)
				if doc, _ := elastic.GetDocument(elastic.AI_RESOURCES_INDEX, modelDocID); len(doc) > 0 {
					if entityJsonStr, ok := doc["entityJson"].(string); ok {

						entityJson := make(map[string]interface{})
						if err := json.Unmarshal([]byte(entityJsonStr), &entityJson); err != nil {
							logger.Print(logger.ERROR, "Error unmarshalling project ai resource json", []string{aiMetaData.TenantID}, err)
							continue
						}

						entityJson["monthly_cost"] = "$ " + strconv.FormatFloat(cost, 'f', 2, 64)

						// all models with a cost should be in used state
						if cost > 0 {
							if used, ok := entityJson["used"].(bool); !ok || !used {
								entityJson["used"] = true
							}
						}

						entityJsonBytes, err := json.Marshal(entityJson)
						if err != nil {
							logger.Print(logger.ERROR, "Error marshalling updated entityJson", []string{aiMetaData.TenantID}, err)
							continue
						}

						updateMeta := map[string]map[string]string{"update": {"_id": modelDocID}}
						updateDoc := map[string]map[string]string{"doc": {"entityJson": string(entityJsonBytes)}}

						metaLine, err := json.Marshal(updateMeta)
						if err != nil {
							logger.Print(logger.ERROR, "Error marshalling update metadata", []string{aiMetaData.TenantID}, err)
							continue
						}

						docLine, err := json.Marshal(updateDoc)
						if err != nil {
							logger.Print(logger.ERROR, "Error marshalling doc update", []string{aiMetaData.TenantID}, err)
							continue
						}

						bulkCloudResourceRequest := string(metaLine) + "\n" + string(docLine) + "\n"
						if err := elastic.BulkDocumentsAPI(aiMetaData.TenantID, elastic.AI_RESOURCES_INDEX, bulkCloudResourceRequest); err != nil {
							logger.Print(logger.ERROR, "Error in update of AI resources model doc", []string{aiMetaData.TenantID}, err, bulkCloudResourceRequest)
							return

						}
					}
				}
			}
		}
	}
}

func CalculateCost(model string, inputTokens, cachedInputTokens, outputTokens int) (float64, error) {
	modelData, exists := CompletionsCost[model]
	if !exists {
		return 0, fmt.Errorf("model %s not found in pricing data", model)
	}

	var modelPricing modelPricingStruct
	modelPricingBytes, _ := json.Marshal(modelData)
	json.Unmarshal(modelPricingBytes, &modelPricing)

	actualInputTokens := inputTokens - cachedInputTokens

	inputCost := (float64(actualInputTokens) / 1000000) * modelPricing.Input
	cachedInputCost := (float64(cachedInputTokens) / 1000000) * modelPricing.CachedInput
	outputCost := (float64(outputTokens) / 1000000) * modelPricing.Output

	totalCost := inputCost + cachedInputCost + outputCost
	return totalCost, nil
}
