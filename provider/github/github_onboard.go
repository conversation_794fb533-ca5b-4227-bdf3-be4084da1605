package github

import (
	"context"
	"strings"
	"time"

	"github.com/google/go-github/v48/github"
	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func onboardGithub(tenantID, accessToken string, githubClient *github.Client, githubCtx context.Context) error {
	envQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"serviceId":"` + common.GITHUB_ID + `"}}],"must_not":[],"should":[]}}}`
	envDocs, err := elastic.ExecuteSearchQuery([]string{elastic.ENVIRONMENTS_INDEX}, envQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Got error while fetching environments", tenantID, err)
		return err
	}
	if len(envDocs) <= 0 {

		var (
			projectList = make([]string, 0)
			tenantName  = ""
		)

		repoPage := 1

		for {

			repos, repoResp, err := githubClient.Repositories.List(githubCtx, "", &github.RepositoryListOptions{
				ListOptions: github.ListOptions{
					Page: repoPage,
				},
			})
			if err != nil {
				logger.Print(logger.ERROR, "Got error listing repos", []string{tenantID}, err)
				break
			}

			for _, repo := range repos {

				for {

					repoOwner := repo.Owner.GetLogin()
					repoName := repo.GetName()
					defaultBranch := repo.GetDefaultBranch()

					branch, _, err := githubClient.Repositories.GetBranch(githubCtx, repoOwner, repoName, defaultBranch, false)
					if err != nil {
						logger.Print(logger.ERROR, "Got error listing branches", []string{tenantID, repoName}, err)
						break
					}

					resp, _, err := githubClient.Git.GetTree(githubCtx, repoOwner, repoName, branch.Commit.GetSHA(), true)
					if err != nil {
						logger.Print(logger.ERROR, "Got error fetching repo tree", []string{tenantID, *repo.Name}, err)
						continue
					}

					for _, file := range resp.Entries {

						filePath := file.GetPath()

						if file.GetType() == "blob" && (strings.HasSuffix(filePath, ".tf") || strings.HasSuffix(filePath, ".hcl") || strings.HasSuffix(filePath, ".tfvars") || strings.HasSuffix(filePath, ".tfvars.json")) {

							if strings.HasSuffix(filePath, "terragrunt.hcl") {
								commits, _, err := githubClient.Repositories.ListCommits(githubCtx, repoOwner, repoName, &github.CommitsListOptions{
									SHA:   defaultBranch,
									Until: time.Now(),
									ListOptions: github.ListOptions{
										Page: 1,
									},
								})
								if err != nil {
									logger.Print(logger.ERROR, "Got error listing commits", []string{tenantID, repoName}, err)
									break
								}

								if len(commits) > 0 {
									commitSha := commits[0].GetSHA()

									repoContent, _, _, err := githubClient.Repositories.GetContents(githubCtx, repoOwner, repoName, filePath, &github.RepositoryContentGetOptions{
										Ref: commitSha,
									})
									if err != nil {
										logger.Print(logger.ERROR, "Got error fetching directories from path for github", []string{tenantID, repoName, filePath, file.GetSHA()}, err)
										return err
									}

									if repoContent != nil {
										contentString, err := repoContent.GetContent()
										if err != nil {
											logger.Print(logger.ERROR, "Got error fetching directory tree for github", []string{tenantID, repoName}, err)
											return err
										}
										logger.Print(logger.INFO, "Processing Terragrunt File", filePath)
										err = common.ProcessTerragruntFile(string(contentString), tenantID, common.GITHUB, &common.GithubApiClient{
											GithubClient: githubClient,
											Context:      githubCtx,
											RepoOwner:    repoOwner,
											RepoName:     repoName,
											CommitSHA:    commitSha,
											TenantID:     tenantID,
											FilePath:     filePath,
											Branch:       defaultBranch,
										})
										if err != nil {
											return err
										}
									}
								}
							}

							projectList = append(projectList, repoName)
						}
					}

				}
			}

			if repoResp.NextPage == 0 {
				break
			}

			repoPage = repoResp.NextPage
		}

		tenantQuery := `{"query":{"bool":{"filter":[{"match":{"id.keyword":"` + tenantID + `"}}]}}}`

		tenantDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TENANTS_INDEX}, tenantQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error fetching tenant doc", []string{tenantID}, err)
			return err
		}

		if len(tenantDocs) > 0 {
			for _, tenantDoc := range tenantDocs {
				if name, ok := tenantDoc["companyName"].(string); ok {
					tenantName = name
				}
			}
		}

		if len(projectList) > 0 {
			elastic.InsertDocument(tenantID, elastic.ENVIRONMENTS_INDEX, common.EnvironmentDoc{
				TenantID:     tenantID,
				ServiceID:    common.GITLAB_ID_INT,
				Name:         tenantName,
				Repositories: []string{},
			})
		}
	}

	return nil
}
