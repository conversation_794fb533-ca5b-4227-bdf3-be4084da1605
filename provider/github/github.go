package github

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/go-github/v48/github"
	"golang.org/x/oauth2"

	"github.com/precize/common"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

func ValidateAuth(githubEnv tenant.GithubEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	authValidation = make(map[string]bool)
	githubCtx := context.Background()

	accessToken, err := getGithubAccessToken(tenantID, githubEnv.Token)
	if err != nil {
		return
	}

	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: accessToken},
	)

	tc := oauth2.NewClient(githubCtx, ts)

	githubClient := github.NewClient(tc)

	repos, _, err := githubClient.Repositories.List(githubCtx, "", &github.RepositoryListOptions{
		ListOptions: github.ListOptions{
			Page: 1,
		},
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error listing repos", []string{tenantID}, err)
		authValidation[accessToken] = false
		return
	}

	isOrgAccessGranted := false

	for _, repo := range repos {

		url := repo.Owner.GetHTMLURL()
		if len(url) > 0 {
			arr := strings.Split(url, "/")
			if len(arr) > 0 {
				orgName := arr[len(arr)-1]

				_, _, err := githubClient.Organizations.Get(githubCtx, orgName)
				if err == nil {
					isOrgAccessGranted = true
					break
				}
			}
		}
	}

	if !isOrgAccessGranted {
		err = fmt.Errorf("Authorization pending – Your GitHub organization has not yet granted access to the Precize application")
		logger.Print(logger.ERROR, "Authorization pending – Your GitHub organization has not yet granted access to the Precize application", []string{tenantID}, err)
		authValidation[accessToken] = false
		return
	}

	authValidation[accessToken] = true
	return
}

func ProcessGithubData(tenantID string, githubEnv tenant.GithubEnvironment, gitStartTime, gitEndTime, tenantStartTime time.Time, preCommitCron bool) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.GITHUB_COMMIT, tenantStartTime)

	githubCtx := context.Background()

	accessToken, err := getGithubAccessToken(tenantID, githubEnv.Token)
	if err != nil {
		return
	}

	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: accessToken},
	)

	tc := oauth2.NewClient(githubCtx, ts)

	githubClient := github.NewClient(tc)

	if defaultTime.Equal(gitStartTime) {
		onboardGithub(tenantID, accessToken, githubClient, githubCtx)
	}

	if !preCommitCron && (gitEndTime.Sub(gitStartTime)) > gitEndTime.Sub(defaultTime) {
		gitStartTime = defaultTime
	}

	logger.Print(logger.INFO, "Fetching github files from "+common.DateTime(gitStartTime)+" to "+common.DateTime(gitEndTime), []string{tenantID})

	repoPage := 1

	for {

		repoMap := make(map[string]struct{})

		for _, r := range githubEnv.Repositories {
			repoMap[r] = struct{}{}
		}

		repos, repoResp, err := githubClient.Repositories.List(githubCtx, "", &github.RepositoryListOptions{
			ListOptions: github.ListOptions{
				Page: repoPage,
			},
		})
		if err != nil {
			logger.Print(logger.ERROR, "Got error listing repos", []string{tenantID}, err)
			return
		}

		for _, repo := range repos {

			repoName := *repo.Name
			repoOwner := *repo.Owner.Login

			if _, ok := repoMap[repoName]; !ok {
				continue
			}

			branchPage := 1

			for {

				branches, branchResp, err := githubClient.Repositories.ListBranches(githubCtx, repoOwner, repoName, &github.BranchListOptions{
					ListOptions: github.ListOptions{
						Page: branchPage,
					},
				})
				if err != nil {
					logger.Print(logger.ERROR, "Got error listing branches", []string{tenantID, repoName}, err)
					return
				}

				for _, branch := range branches {

					commitPage := 1

					for {

						commits, commitResp, err := githubClient.Repositories.ListCommits(githubCtx, repoOwner, repoName, &github.CommitsListOptions{
							SHA:   *branch.Name,
							Since: gitStartTime,
							Until: gitEndTime,
							ListOptions: github.ListOptions{
								Page: commitPage,
							},
						})
						if err != nil {
							logger.Print(logger.ERROR, "Got error listing commits", []string{tenantID, repoName}, err)
							return
						}

						for _, commit := range commits {

							repoCommit := RepoCommit{
								SHA:       *commit.SHA,
								RepoName:  repoName,
								RepoOwner: repoOwner,
								Branch:    *branch.Name,
							}

							if preCommitCron && common.CommitCollected(tenantID, repoCommit.SHA) {
								continue
							}

							if err = processGithubCommit(githubCtx, githubClient, repoCommit, tenantID, preCommitCron); err != nil {
								continue
							}
						}

						if commitResp.NextPage == 0 {
							break
						}

						commitPage = commitResp.NextPage
					}
				}

				if branchResp.NextPage == 0 {
					break
				}

				branchPage = branchResp.NextPage
			}

		}

		if repoResp.NextPage == 0 {
			break
		}

		repoPage = repoResp.NextPage
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.GITHUB_COMMIT, gitEndTime)
}

func getGithubAccessToken(tenantID, token string) (string, error) {

	type TokenInfo struct {
		AccessToken string `json:"access_token"`
		TokenType   string `json:"token_type"`
		Scope       string `json:"scope"`
	}

	type GitHubTokenInfo struct {
		TokenInfo     TokenInfo `json:"tokenInfo"`
		EnvironmentID string    `json:"environmentId"`
	}

	var gitHubTokenInfo GitHubTokenInfo

	if err := json.Unmarshal([]byte(token), &gitHubTokenInfo); err != nil {
		logger.Print(logger.ERROR, "Error while unmarshalling github token", tenantID, err)
		return "", err
	}

	return gitHubTokenInfo.TokenInfo.AccessToken, nil
}
