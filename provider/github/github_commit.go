package github

import (
	"context"
	"encoding/json"
	"strings"

	"github.com/google/go-github/v48/github"
	sanathyaml "github.com/sanathkr/yaml"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type RepoCommit struct {
	SHA       string
	RepoName  string
	RepoOwner string
	Branch    string
}

func processGithubCommit(githubCtx context.Context, githubClient *github.Client, repoCommit RepoCommit, tenantID string, preCommitCron bool) error {

	commit, _, err := githubClient.Repositories.GetCommit(githubCtx, repoCommit.RepoOwner, repoCommit.RepoName, repoCommit.SHA, nil)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting commit", []string{tenantID, repoCommit.RepoName}, err)
		return err
	}
	gitCommitDiff, _, err := githubClient.Repositories.GetCommitRaw(githubCtx, repoCommit.RepoOwner, repoCommit.RepoName, repoCommit.SHA, github.RawOptions{
		Type: github.Patch,
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting commit raw", []string{tenantID, repoCommit.RepoName}, err)
		return err
	}

	for _, commitFile := range commit.Files {

		filePath := *commitFile.Filename

		if strings.HasSuffix(filePath, ".yaml") || strings.HasSuffix(filePath, ".yml") ||
			strings.HasSuffix(filePath, ".json") || strings.HasSuffix(filePath, ".tf") || strings.HasSuffix(filePath, ".tfvars") || strings.HasSuffix(filePath, ".tfvars.json") || strings.HasSuffix(filePath, ".sh") {

			var content []byte

			repoContent, _, _, err := githubClient.Repositories.GetContents(githubCtx, repoCommit.RepoOwner, repoCommit.RepoName, filePath, &github.RepositoryContentGetOptions{
				Ref: repoCommit.SHA,
			})
			if err != nil && !(commitFile.GetStatus() == "removed" && strings.Contains(err.Error(), "Not Found")) {
				logger.Print(logger.ERROR, "Got error getting repo content", []string{tenantID, repoCommit.RepoName}, err)
				continue
			}

			contentString := ""

			if commitFile.GetStatus() == "removed" || repoContent.Content != nil {

				if commitFile.GetStatus() != "removed" {
					contentString, err = repoContent.GetContent()
					if err != nil {
						logger.Print(logger.ERROR, "Got error getting content", []string{tenantID, repoCommit.RepoName}, err)
						continue
					}
				}

				content = []byte(contentString)

				fileStatus := "modified"
				switch commitFile.GetStatus() {
				case "added", "renamed", "copied":
					fileStatus = "created"
				case "removed":
					fileStatus = "deleted"
				case "modified", "changed", "unchanged":
				}

				pathSlice := strings.Split(filePath, "/")
				fileName := pathSlice[len(pathSlice)-1]

				if strings.HasSuffix(filePath, ".tf") || strings.HasSuffix(filePath, ".tfvars") || strings.HasSuffix(filePath, ".sh") {

					// Terraform

					var (
						docID string
						csp   string
					)

					if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
						CommitID:      *commit.SHA,
						CommitSummary: *commit.Commit.Message,
						Author:        *commit.Commit.Author.Name,
						AuthorEmail:   *commit.Commit.Author.Email,
						Filename:      filePath,
						GitFileHash:   *commitFile.SHA,
						CommitTime:    elastic.DateTime(*commit.Commit.Author.Date),
						FileStatus:    fileStatus,
						RepoName:      repoCommit.RepoName,
						Branch:        repoCommit.Branch,
						IACType:       common.TERRAFORM,
						GitClient:     common.GITHUB,
						TenantID:      tenantID,
						FileContent:   contentString,
					}); err != nil {
						continue
					}

					csp, err = common.ProcessTerraformCommitResources(contentString, gitCommitDiff, docID, tenantID, elastic.DateTime(*commit.Commit.Author.Date), common.GITHUB, &common.GithubApiClient{
						GithubClient: githubClient,
						Context:      githubCtx,
						RepoOwner:    repoCommit.RepoOwner,
						RepoName:     repoCommit.RepoName,
						CommitSHA:    repoCommit.SHA,
						TenantID:     tenantID,
						FilePath:     filePath,
						FileStatus:   fileStatus,
						Branch:       repoCommit.Branch,
					})
					if err != nil {
						logger.Print(logger.ERROR, "Error while Processing TF Commits", []string{tenantID, repoCommit.RepoName, repoCommit.SHA}, err)
						continue
					}

					if strings.HasSuffix(fileName, "variables.tf") || strings.HasSuffix(fileName, "var.tf") || strings.HasSuffix(fileName, ".tfvars") || strings.HasSuffix(fileName, ".tfvars.json") || strings.HasSuffix(fileName, "variable.tf") || strings.HasSuffix(fileName, "vars.tf") || strings.HasSuffix(fileName, ".sh") {
						continue
					}

					if preCommitCron {

						commitTime := *commit.Commit.Author.Date

						err := common.ProcessPreCommitEvents(tenantID, csp, commitTime)
						if err != nil {
							logger.Print(logger.ERROR, "Error while Processing Pre Commit Cron TF Commits", []string{tenantID, repoCommit.RepoName, repoCommit.SHA}, err)
							continue
						}
					}

				} else {

					// Cloudformation/ARM

					if !strings.HasSuffix(filePath, ".json") {

						content, err = sanathyaml.YAMLToJSON(content)
						if err != nil {
							logger.Print(logger.ERROR, "Got error converting to json", []string{tenantID, repoCommit.RepoName}, err)
							// continue
						}
					}

					if len(content) > 0 && strings.HasPrefix(string(content), "{") {
						var r map[string]interface{}

						minifiedJSONHash, err := common.GetMinifiedJSONFileHash(content)
						if err != nil {
							logger.Print(logger.ERROR, "Got error generating minifed json hash", []string{tenantID, repoCommit.RepoName}, err)
							// continue
						}

						err = json.Unmarshal(content, &r)
						if err != nil {
							logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, repoCommit.RepoName}, err)
							continue
						}

						if _, ok := r["AWSTemplateFormatVersion"]; ok {

							priorityConfigs, err := common.ProcessCftTemplate(r, tenantID)
							if err != nil {
								logger.Print(logger.INFO, "Error parsing cft tempalte", []string{tenantID, repoCommit.RepoName}, err)
								continue
							}

							var docID string

							if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
								CommitID:         *commit.SHA,
								CommitSummary:    *commit.Commit.Message,
								Author:           *commit.Commit.Author.Name,
								AuthorEmail:      *commit.Commit.Author.Email,
								Filename:         filePath,
								GitFileHash:      *commitFile.SHA,
								MinifiedJSONHash: minifiedJSONHash,
								CommitTime:       elastic.DateTime(*commit.Commit.Author.Date),
								FileStatus:       fileStatus,
								RepoName:         repoCommit.RepoName,
								Branch:           repoCommit.Branch,
								IACType:          "aws_cft",
								GitClient:        "github",
								TenantID:         tenantID,
								FileContent:      contentString,
								PriorityConfigs:  priorityConfigs,
							}); err != nil {
								continue
							}

							if err = common.MapCommitToCFTResources(tenantID, *commitFile.SHA, common.GitUser{
								Name:   common.GitIdentity(*commit.Commit.Author.Email, *commit.Commit.Author.Name, tenantID),
								Client: "github", Action: fileStatus, CommitTime: elastic.DateTime(*commit.Commit.Author.Date), DocID: docID},
							); err != nil {
								continue
							}

						} else if _, ok = r["$schema"]; ok {

							var docID string

							if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
								CommitID:         *commit.SHA,
								CommitSummary:    *commit.Commit.Message,
								Author:           *commit.Commit.Author.Name,
								AuthorEmail:      *commit.Commit.Author.Email,
								Filename:         filePath,
								GitFileHash:      *commitFile.SHA,
								MinifiedJSONHash: minifiedJSONHash,
								CommitTime:       elastic.DateTime(*commit.Commit.Author.Date),
								FileStatus:       fileStatus,
								RepoName:         repoCommit.RepoName,
								Branch:           repoCommit.Branch,
								IACType:          "azure_arm",
								GitClient:        "github",
								TenantID:         tenantID,
								FileContent:      contentString,
							}); err != nil {
								continue
							}

							if err = common.MapCommitToARMResources(tenantID, minifiedJSONHash, common.GitUser{
								Name:   common.GitIdentity(*commit.Commit.Author.Email, *commit.Commit.Author.Name, tenantID),
								Client: "github", Action: fileStatus, CommitTime: elastic.DateTime(*commit.Commit.Author.Date), DocID: docID},
							); err != nil {
								continue
							}

						}
					}
				}
			}
		}
	}

	return nil
}
