package elasticsearch

import (
	"fmt"
	"time"

	"github.com/precize/analyzer/internal/esjson"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

const EVENT_ERROR_STATUS_FAILURE = "0"

type Event struct {
	ID               string                 `json:"id"`
	AccountID        string                 `json:"accountId"`
	TenantID         string                 `json:"tenantId"`
	OriginalUsername string                 `json:"originalUsername"`
	Username         string                 `json:"username"`
	ResourceName     string                 `json:"resourceName"`
	ResourceType     string                 `json:"resourceType"`
	EventSource      []string               `json:"eventSource"`
	SourceIp         []string               `json:"sourceIp"`
	SourceApp        []string               `json:"sourceApp"`
	ServiceCode      string                 `json:"serviceCode"`
	CreatedAt        string                 `json:"createdAt"`
	LastUpdatedAt    string                 `json:"updatedAt"`
	EventName        []string               `json:"eventName"`
	ErrorCount       int                    `json:"errorCount"`
	Region           []string               `json:"region"`
	Count            int                    `json:"count"`
	AssumeRole       []string               `json:"assumedRole"`
	Additional       map[string]interface{} `json:"additional"`
	DocIDs           []string               `json:"-"`
}

// GetExistingEvents retrieves existing events from the specified index
//
// It returns a list of found events, a list of not found document IDs and an error
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		events, notFoundIDs, err := es.GetExistingEvents("events_store", []string{"1", "2", "3"})
//		if err != nil {
//			log.Fatalf("Error getting existing events: %s", err)
//		}
//	}
func (es *Client) GetExistingEvents(indexName string, docIds []string) ([]Event, []string, error) {
	foundDocs, notFoundIds, err := es.CheckDocumentExistence(indexName, docIds)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to check documents: %w", err)
	}

	var events []Event
	for _, doc := range foundDocs {
		events = append(events, doc.Source)
	}

	return events, notFoundIds, nil
}

type GetEventsResponse struct {
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
		Hits []struct {
			Source Event `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

func (es *Client) GetEvents(tid string, startTime, endTime time.Time) ([]Event, error) {
	newStartTime := startTime.UTC().Format(utils.ESTimeLayout)
	newEndTime := endTime.UTC().Format(utils.ESTimeLayout)
	params := esjson.QueryParams{
		TenantId:  tid,
		StartTime: newStartTime,
		EndTime:   newEndTime,
	}
	query, err := esjson.LoadQuery(esjson.GET_DATA_BY_TENANT_ID, &params)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("failed to load query: %s", err))
		return nil, fmt.Errorf("failed to load query: %w", err)
	}

	_events, err := ExecuteQuery[GetEventsResponse](es, EVENTS_STORE_INDEX, query)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("failed to execute query: %s", err))
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}

	events := make([]Event, len(_events.Hits.Hits))
	for i, hit := range _events.Hits.Hits {
		events[i] = hit.Source
	}
	return events, nil
}
