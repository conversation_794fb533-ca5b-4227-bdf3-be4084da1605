package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	elasticsearch "github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"
	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/esjson"
	"github.com/precize/logger"
)

const (
	TENANTS_INDEX          = "tenants"
	EVENTS_STORE_INDEX     = "events_store"
	ENTITY_BEHAVIOUR_INDEX = "entity_behaviour"
	ANOMALIES_INDEX        = "anomalies"
	CLOUD_ACTIVITY_INDEX   = "cloud_activity"
	CLOUD_INCIDENTS_INDEX  = "cloud_incidents"
)

type Client struct {
	*elasticsearch.Client
}

// Global client instance
var client *Client

// NewClient creates a new elasticsearch client
//
// If a client has already been created, it returns the existing client
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		if _, err := es.Ping(); err != nil {
//			log.Fatalf("Error pinging elasticsearch: %s", err)
//		}
//	}
func NewClient() *Client {
	elasticConfig := config.GetElasticsearchConfig()

	var cert []byte
	var err error

	if len(elasticConfig.CACertPath) > 0 {
		if cert, err = os.ReadFile(elasticConfig.CACertPath); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error reading CA cert file: %s", err))
			return nil
		}
	}
	es, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: []string{elasticConfig.Scheme + "://" + elasticConfig.Host + ":" + fmt.Sprint(elasticConfig.Port)},
		Username:  elasticConfig.Username,
		Password:  elasticConfig.Password,
		CACert:    cert,
	})

	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error creating the client: %s", err))
		return nil
	}

	if client == nil {
		client = &Client{
			Client: es,
		}
	}

	return client
}

// EnsureIndexExists ensures that the specified index exists
//
// If the index does not exist, it creates the index
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		if err := es.EnsureIndexExists(elasticsearch.EventsStoreIndex, ""); err != nil {
//			log.Fatalf("Error ensuring events index exists: %s", err)
//		}
//	}
func (es *Client) EnsureIndexExists(indexName string, indexMapping string) error {
	ctx := context.Background()

	res, err := es.Indices.Exists([]string{indexName}, es.Indices.Exists.WithContext(ctx))
	if err != nil {
		return fmt.Errorf("error checking if index exists: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode == 200 {
		return nil
	}

	if res.StatusCode == 404 {
		logger.Print(logger.INFO, "Index does not exist, creating...")

		var createRes *esapi.Response

		if indexMapping != "" {
			createRes, err = es.Indices.Create(
				indexName,
				es.Indices.Create.WithBody(strings.NewReader(indexMapping)),
				es.Indices.Create.WithContext(ctx),
			)
		} else {
			createRes, err = es.Indices.Create(
				indexName,
				es.Indices.Create.WithContext(ctx),
			)
		}

		if err != nil {
			return fmt.Errorf("error creating index: %w", err)
		}
		defer createRes.Body.Close()

		if createRes.IsError() {
			return fmt.Errorf("error response from Elasticsearch when creating index: %s", createRes.String())
		}

		logger.Print(logger.INFO, "Index created successfully")
		return nil
	}

	return fmt.Errorf("unexpected response status code: %d", res.StatusCode)
}

// EnsureIndicesExist ensures that the specified indices exist.
// If an index does not exist, it creates the index with the provided mapping.
//
//	func main() {
//		mappings := map[string]esjson.MappingType{
//			elasticsearch.CLOUD_ACTIVITY_INDEX:           esjson.EventsMapping,
//			elasticsearch.TENANTS_INDEX:          esjson.TenantsMapping,
//			elasticsearch.EVENTS_STORE_INDEX:     esjson.EventsStoreMapping,
//			elasticsearch.ENTITY_BEHAVIOUR_INDEX: esjson.EntityBehaviourMapping,
//		}
//
//		if err := es.EnsureIndicesExist(mappings); err != nil {
//			log.Fatalf("Error ensuring indices exist: %s", err)
//		}
//	}
func (es *Client) EnsureIndicesExist(indices map[string]esjson.MappingType) error {
	ctx := context.Background()

	for indexName, indexMappingType := range indices {
		res, err := es.Indices.Exists([]string{indexName}, es.Indices.Exists.WithContext(ctx))
		defer res.Body.Close()
		if err != nil {
			return err
		}

		if res.StatusCode == 200 {
			continue
		}

		if res.StatusCode == 404 {
			logger.Print(logger.INFO, fmt.Sprintf("Index does not exist, creating... %s", indexName))

			var createRes *esapi.Response

			indexMapping, err := esjson.LoadMapping(indexMappingType)
			if err != nil {
				return fmt.Errorf("error loading index mapping: %w", err)
			}

			if indexMapping != "" {
				createRes, err = es.Indices.Create(
					indexName,
					es.Indices.Create.WithBody(
						strings.NewReader(indexMapping),
					),
					es.Indices.Create.WithContext(ctx),
				)
			} else {
				createRes, err = es.Indices.Create(
					indexName,
					es.Indices.Create.WithContext(ctx),
				)
			}

			defer createRes.Body.Close()

			if err != nil {
				return fmt.Errorf("error creating index '%s': %w", indexName, err)
			}

			if createRes.IsError() {
				return fmt.Errorf("error response from Elasticsearch when creating index '%s': %s", indexName, createRes.String())
			}

			logger.Print(logger.INFO, fmt.Sprintf("Index created successfully %s", indexName))
			continue
		}

		return fmt.Errorf("unexpected response status code for index '%s': %d", indexName, res.StatusCode)
	}

	return nil
}

// DeleteIndex deletes the specified index
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		if err := es.DeleteIndex(elasticsearch.EventsStoreIndex); err != nil {
//			log.Fatalf("Error deleting events index: %s", err)
//		}
//	}
func (es *Client) DeleteIndex(indexName string) error {
	ctx := context.Background()
	res, err := es.Indices.Delete([]string{indexName}, es.Indices.Delete.WithContext(ctx))
	if err != nil {
		return fmt.Errorf("error deleting index: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("error response from Elasticsearch during index deletion: %s", res.String())
	}

	fmt.Println("Index deleted successfully")
	return nil
}

type Doc struct {
	ID     string `json:"_id"`
	Index  string `json:"_index"`
	Found  bool   `json:"found"`
	Source Event  `json:"_source"`
}

type MgetResponse struct {
	Docs []Doc `json:"docs"`
}

// CheckDocumentExistence checks if the specified documents exist in the specified index
//
// It returns the documents that were found and the IDs of the documents that were not found
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		foundDocs, notFoundIDs, err := es.CheckDocumentExistence("events", []string{"1", "2", "3"})
//		if err != nil {
//			log.Fatalf("Error checking document existence: %s", err)
//		}
//	}
func (es *Client) CheckDocumentExistence(indexName string, ids []string) (foundDocs []Doc, notFoundIDs []string, err error) {
	const batchSize = 10000
	ctx := context.Background()

	// Process in batches
	for i := 0; i < len(ids); i += batchSize {
		end := i + batchSize
		if end > len(ids) {
			end = len(ids)
		}

		batchIDs := ids[i:end]
		batchDocs := make([]map[string]string, len(batchIDs))
		for j, id := range batchIDs {
			batchDocs[j] = map[string]string{"_id": id}
		}

		requestBody, err := json.Marshal(map[string]interface{}{"docs": batchDocs})
		if err != nil {
			return nil, nil, fmt.Errorf("error marshalling Mget request body for batch %d-%d: %w", i, end, err)
		}

		res, err := es.Mget(
			strings.NewReader(string(requestBody)),
			es.Mget.WithContext(ctx),
			es.Mget.WithIndex(indexName),
		)
		if err != nil {
			return nil, nil, fmt.Errorf("error checking document existence for batch %d-%d: %w", i, end, err)
		}

		var response MgetResponse
		err = func() error {
			defer res.Body.Close()
			if res.IsError() {
				return fmt.Errorf("error response from Elasticsearch for batch %d-%d: %s", i, end, res.Status())
			}
			if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
				return fmt.Errorf("error parsing response for batch %d-%d: %w", i, end, err)
			}
			return nil
		}()
		if err != nil {
			return nil, nil, err
		}

		// Process batch results
		for _, doc := range response.Docs {
			if doc.Found {
				foundDocs = append(foundDocs, doc)
			} else {
				notFoundIDs = append(notFoundIDs, doc.ID)
			}
		}
	}

	return foundDocs, notFoundIDs, nil
}

// ExecuteQuery executes the specified query string on the specified index
// and returns the response
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		_, err := es.ExecuteQuery[map[string]any]("events", `{"query": {"match_all": {}}}`)
//		if err != nil {
//	   		log.Fatalf("Error executing query: %s", err)
//		}
//	}
func ExecuteQuery[T any](es *Client, index, queryString string) (*T, error) {
	res, err := es.Search(
		es.Search.WithIndex(index),
		es.Search.WithBody(strings.NewReader(queryString)),
		es.Search.WithPretty(),
	)
	if err != nil {
		return nil, fmt.Errorf("error getting response: %s", err)
	}
	defer res.Body.Close()

	if res.IsError() || res.StatusCode != 200 || res.Body == nil {
		return nil, fmt.Errorf("error response: %s", res.Status())
	}

	var response T
	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("error parsing the response body: %w", err)
	}

	return &response, nil
}

func ExecuteLargeSearchQuery[T any](es *Client, index string, query string, searchAfter interface{}) (*T, interface{}, error) {
	var (
		size         = 10_000
		sortResponse interface{}
	)

	var queryMap map[string]interface{}
	if err := json.Unmarshal([]byte(query), &queryMap); err != nil {
		return nil, nil, fmt.Errorf("error unmarshalling query: %w", err)
	}

	if _, ok := queryMap["sort"]; !ok {
		queryMap["sort"] = []map[string]interface{}{
			{"_id": map[string]interface{}{"order": "desc"}},
		}
	}

	if searchAfter != nil {
		queryMap["search_after"] = searchAfter
	}

	queryBytes, err := json.Marshal(queryMap)
	if err != nil {
		return nil, nil, fmt.Errorf("error marshalling query: %w", err)
	}

	queryString := string(queryBytes)
	res, err := es.Search(
		es.Search.WithIndex(index),
		es.Search.WithBody(strings.NewReader(queryString)),
		es.Search.WithSize(size),
		es.Search.WithPretty(),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("error executing search: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() || res.StatusCode != 200 || res.Body == nil {
		return nil, nil, fmt.Errorf("error response: %s", res.Status())
	}

	var response T
	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return nil, nil, fmt.Errorf("error decoding response body: %w", err)
	}

	var hits struct {
		Hits struct {
			Hits []struct {
				ID     string                 `json:"_id"`
				Source map[string]interface{} `json:"_source"`
				Sort   interface{}            `json:"sort"`
			} `json:"hits"`
		} `json:"hits"`
	}
	if err := json.NewDecoder(res.Body).Decode(&hits); err != nil {
		return nil, nil, fmt.Errorf("error decoding hits from response: %w", err)
	}

	documents := make(map[string]map[string]interface{})
	for _, hit := range hits.Hits.Hits {
		documents[hit.ID] = hit.Source
		sortResponse = hit.Sort
	}

	return &response, sortResponse, nil
}

type BaseDoc[T any] struct {
	ID     string `json:"_id"`
	Index  string `json:"_index"`
	Found  bool   `json:"found"`
	Source T      `json:"_source"`
}

func CheckDocumentExistence[E any](es *Client, indexName string, ids []string) (foundDocs []BaseDoc[E], notFoundIDs []string, err error) {
	const batchSize = 10000
	ctx := context.Background()

	// Process in batches
	for i := 0; i < len(ids); i += batchSize {
		end := i + batchSize
		if end > len(ids) {
			end = len(ids)
		}

		batchIDs := ids[i:end]
		batchDocs := make([]map[string]string, len(batchIDs))
		for j, id := range batchIDs {
			batchDocs[j] = map[string]string{"_id": id}
		}

		requestBody, err := json.Marshal(map[string]interface{}{"docs": batchDocs})
		if err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("error marshalling Mget request body for batch %d-%d", i, end), err)
			return nil, nil, fmt.Errorf("error marshalling Mget request body for batch %d-%d: %w", i, end, err)
		}

		res, err := es.Mget(
			strings.NewReader(string(requestBody)),
			es.Mget.WithContext(ctx),
			es.Mget.WithIndex(indexName),
		)
		if err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("error checking document existence for batch %d-%d", i, end), err)
			return nil, nil, fmt.Errorf("error checking document existence for batch %d-%d: %w", i, end, err)
		}

		var response struct {
			Docs []BaseDoc[E] `json:"docs"`
		}

		err = func() error {
			defer res.Body.Close()
			if res.IsError() {
				logger.Print(logger.ERROR, fmt.Sprintf("error response from Elasticsearch for batch %d-%d", i, end), res.Status())
				return fmt.Errorf("error response from Elasticsearch for batch %d-%d: %s", i, end, res.Status())
			}
			if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
				logger.Print(logger.ERROR, fmt.Sprintf("error parsing response for batch %d-%d", i, end), err)
				return fmt.Errorf("error parsing response for batch %d-%d: %w", i, end, err)
			}
			return nil
		}()
		if err != nil {
			return nil, nil, err
		}

		// Process batch results
		for _, doc := range response.Docs {
			if doc.Found {
				foundDocs = append(foundDocs, doc)
			} else {
				notFoundIDs = append(notFoundIDs, doc.ID)
			}
		}
	}

	return foundDocs, notFoundIDs, nil
}

func GetExistingDocs[T any](es *Client, indexName string, docIds []string) ([]T, []string, error) {
	foundDocs, notFoundIds, err := CheckDocumentExistence[T](es, indexName, docIds)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("failed to check documents for index %s", indexName), err)
		return nil, nil, fmt.Errorf("failed to check documents: %w", err)
	}

	var source []T
	for _, doc := range foundDocs {
		source = append(source, doc.Source)
	}

	return source, notFoundIds, nil
}
