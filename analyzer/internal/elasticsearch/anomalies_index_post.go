package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/analyzer/internal/utils"
)

type AnomalyType string

const (
	ANOMALY_SERVERITY_HIGH   = "High"
	ANOMALY_SERVERITY_MEDIUM = "Medium"
	ANOMALY_SERVERITY_LOW    = "Low"

	// Anomaly types
	HIGH_ERROR_COUNT              AnomalyType = "HighErrorCount"
	UNUSUAL_EVENT_SOURCE          AnomalyType = "UnusualEventSource"
	UNUSUAL_REGION                AnomalyType = "UnusualRegion"
	UNUSUAL_SOURCE_APP            AnomalyType = "UnusualSourceApp"
	SHARED_SERVICE_ACCOUNT_VIA_IP AnomalyType = "SharedServiceAccountViaIP"
	HIGH_FREQUENCY_OF_EVENTS      AnomalyType = "HighFrequencyOfEvents"
	PERSONAL_EMAIL_USAGE          AnomalyType = "PersonalEmailUsage"
)

var anomalyIssue = map[AnomalyType]string{
	HIGH_ERROR_COUNT:              "High Error Count",
	UNUSUAL_EVENT_SOURCE:          "Unusual Event Source",
	UNUSUAL_REGION:                "Unusual Region",
	UNUSUAL_SOURCE_APP:            "Unusual Source App",
	SHARED_SERVICE_ACCOUNT_VIA_IP: "Shared Service Account Via IP",
	HIGH_FREQUENCY_OF_EVENTS:      "High Frequency of Events",
	PERSONAL_EMAIL_USAGE:          "Personal Email Usage",
}

func GetAnomalyIssue(a AnomalyType) (string, bool) {
	issue, ok := anomalyIssue[a]
	return issue, ok
}

var anomalyDescriptions = map[AnomalyType]string{
	HIGH_ERROR_COUNT:              "High number of errors detected in a short period.",
	UNUSUAL_EVENT_SOURCE:          "Event source appears uncommon for this account.",
	UNUSUAL_REGION:                "Activity from a region not usually observed.",
	UNUSUAL_SOURCE_APP:            "Source application is not typically used.",
	SHARED_SERVICE_ACCOUNT_VIA_IP: "Service account appears shared across IPs.",
	HIGH_FREQUENCY_OF_EVENTS:      "Unusually high frequency of events observed.",
	PERSONAL_EMAIL_USAGE:          "Using personal email is not usually allowed",
}

func GetAnomalyDescription(a AnomalyType) (string, bool) {
	desc, ok := anomalyDescriptions[a]
	return desc, ok
}

type Anomaly struct {
	ID                 string      `json:"id"`
	AccountID          string      `json:"accountId"`
	TenantID           string      `json:"tenantId"`
	Username           string      `json:"username"`
	AnomalyType        AnomalyType `json:"anomalyType"`
	AnomalyDescription string      `json:"anomalyDescription"`
	RefDoc             []string    `json:"refDoc"`
	Count              int         `json:"count"`
	LastDetectedAt     string      `json:"lastDetectedAt"`
	FirstDetectedAt    string      `json:"firstDetectedAt"`
	AnomalyEntity      string      `json:"anomalyEntity"`
	AdditionalDetails  string      `json:"additionalDetails"`
	ServiceCode        string      `json:"serviceCode"`
	Severity           string      `json:"severity"`
}

func (es *Client) StoreAnomalies(anomalies []Anomaly) error {
	ctx := context.Background()

	// Use a string builder to create a bulk request payload
	var bulkBuilder strings.Builder

	for _, anomaly := range anomalies {
		// Prepare the bulk index action and document
		actionLine := fmt.Sprintf(`{"index":{"_index":"%s","_id":"%s"}}`, ANOMALIES_INDEX, anomaly.ID)
		docLine := utils.ToJSON(anomaly)

		bulkBuilder.WriteString(actionLine + "\n")
		bulkBuilder.WriteString(docLine + "\n")
	}

	if bulkBuilder.Len() == 0 {
		return nil // Nothing to index
	}

	// Execute the bulk request
	res, err := es.Bulk(
		strings.NewReader(bulkBuilder.String()),
		es.Client.Bulk.WithContext(ctx),
		es.Client.Bulk.WithIndex(ANOMALIES_INDEX),
	)

	if err != nil {
		return fmt.Errorf("failed to execute bulk request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		return fmt.Errorf("bulk request failed with status: %s", res.Status())
	}

	if res.IsError() {
		var raw map[string]interface{}
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			return fmt.Errorf("bulk request failed and error response could not be parsed: %w", err)
		}
		return fmt.Errorf("bulk request failed: %v", raw)
	}

	return nil
}
