package common

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/mail"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

const (
	baseURL = "https://api.openai.com/v1"
)

type OpenAIClient struct {
	APIKey string
}

var openAIClient OpenAIClient

type OpenAIRequest struct {
	Model    string          `json:"model"`
	Messages []OpenAIMessage `json:"messages"`
	Top      float64         `json:"top_p"`
}

type OpenAIResponse struct {
	Choices []OpenAIChoice `json:"choices"`
}

type OpenAIChoice struct {
	Message OpenAIMessage `json:"message"`
}

type OpenAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type TextLookupInsertDoc struct {
	Text       string `json:"text"`
	HasName    bool   `json:"hasName"`
	Email      string `json:"email"`
	Name       string `json:"name"`
	TenantID   string `json:"tenantId"`
	InsertTime string `json:"insertTime"`
	Response   string `json:"response"`
	Category   string `json:"category"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Conversation struct {
	Messages []Message `json:"messages"`
}

type Completion struct {
	Prompt     string `json:"prompt"`
	Completion string `json:"completion"`
}

type OpenAIFile struct {
	ID        string `json:"id"`
	CreatedAt int64  `json:"created_at"`
	Object    string `json:"object"`
	Bytes     int64  `json:"bytes"`
	FileName  string `json:"filename"`
	Purpose   string `json:"purpose"`
	Type      string `json:"file_type"`
}

type MessageReq struct {
	Role        string       `json:"role"`
	Content     string       `json:"content"`
	Attachments []Attachment `json:"attachments,omitempty"`
}

type Attachment struct {
	FileID string `json:"file_id"`
	Tools  []Tool `json:"tools"`
}

type Tool struct {
	Type string `json:"type"`
}

type ThreadResponse struct {
	ID            string              `json:"id"`
	ThreadID      string              `json:"thread_id"`
	Object        string              `json:"object"`
	CreatedAt     int64               `json:"created_at"`
	ToolResources ThreadToolResources `json:"tool_resources"`
	Metadata      map[string]string   `json:"metadata"`
}

type ThreadToolResources struct {
	CodeInterpreter CodeInterpreterResources `json:"code_interpreter,omitempty"`
	FileSearch      FileSearchResources      `json:"file_search,omitempty"`
}

type CodeInterpreterResources struct {
	FileIDs []string `json:"file_ids"`
}

type FileSearchResources struct {
	VectorStoreIDs []string `json:"vector_store_ids"`
}

type MessageResponse struct {
	Data    []MessageRespData `json:"data"`
	LastID  string            `json:"last_id"`
	HasMore bool              `json:"has_more"`
}

type MessageRespData struct {
	ID           string            `json:"id"`
	Object       string            `json:"object"`
	CreatedAt    int64             `json:"created_at"`
	ThreadID     string            `json:"thread_id"`
	Status       string            `json:"status"`
	CompletedAt  int64             `json:"completed_at,omitempty"`
	IncompleteAt int64             `json:"incomplete_at,omitempty"`
	Role         string            `json:"role"`
	Content      []Content         `json:"content"`
	AssistantID  string            `json:"assistant_id,omitempty"`
	RunID        string            `json:"run_id,omitempty"`
	Attachments  []Attachment      `json:"attachments,omitempty"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

type Content struct {
	Type    string  `json:"type"`
	Text    Text    `json:"text,omitempty"`
	Refusal Refusal `json:"refusal,omitempty"`
}

type Text struct {
	Value string `json:"value"`
}

type Refusal struct {
	Content string `json:"content"`
}

type ThreadRequest struct {
	AssistantID string `json:"assistant_id"`
	Thread      Thread `json:"thread"`
}

type Thread struct {
	Messages []MessageReq `json:"messages"`
}

func InitializeOpenAI() {

	if len(config.AppConfig.OpenAI.APIKey) <= 0 {
		return
	}

	decodedAPIKey, err := base64.StdEncoding.DecodeString(config.AppConfig.OpenAI.APIKey)
	if err == nil {
		decryptedKey, err := DecryptTextAES(decodedAPIKey)
		if err == nil {
			openAIClient.APIKey = string(decryptedKey)
		} else {
			openAIClient.APIKey = config.AppConfig.OpenAI.APIKey
		}
	} else {
		logger.Print(logger.ERROR, "Failed to decode base64", err)
		openAIClient.APIKey = config.AppConfig.OpenAI.APIKey
	}
}

func requestOpenAIChat(reqMessage string, tenantID string, modelName ...string) (respMessage string, err error) {

	randomStringForLogging := SimpleRandomString(6) // Since this function can be called in multiple threads, need to connect request with response for logging

	logger.Print(logger.INFO, "OpenAI Request", []string{tenantID}, randomStringForLogging, reqMessage)

	var (
		resp                []byte
		rateLimitWaitPeriod = 1
		retryCount          = 1
		maxRetry            = 3
	)

	model := "gpt-4o-mini"
	if len(modelName) > 0 && len(modelName[0]) > 0 {
		model = modelName[0]
	}

	var openAIRequest = OpenAIRequest{
		Model: model,
		Messages: []OpenAIMessage{
			OpenAIMessage{
				Role:    "user",
				Content: reqMessage,
			},
		},
		Top: 0.7,
	}

	if len(openAIClient.APIKey) <= 0 {
		err = errors.New("Empty token")
		logger.Print(logger.INFO, "Token invalid", err)
		return
	}

	headers := map[string]string{"Authorization": "Bearer " + openAIClient.APIKey}

	for {

		var buf bytes.Buffer
		if err = json.NewEncoder(&buf).Encode(openAIRequest); err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", err)
			return
		}

		resp, err = transport.SendRequest("POST", "https://api.openai.com/v1/chat/completions", nil, headers, &buf)
		if err != nil {
			if strings.Contains(err.Error(), strconv.Itoa(http.StatusTooManyRequests)) {
				// Rate limit reached
				if retryCount > maxRetry {
					return
				}
				logger.Print(logger.INFO, "Rate limit reached. Retrying in", rateLimitWaitPeriod, "minute(s)")
				time.Sleep(time.Duration(rateLimitWaitPeriod) * time.Minute)
				rateLimitWaitPeriod++
				retryCount++
				continue
			} else if strings.Contains(err.Error(), strconv.Itoa(http.StatusInternalServerError)) || strings.Contains(err.Error(), strconv.Itoa(http.StatusNotImplemented)) || strings.Contains(err.Error(), strconv.Itoa(http.StatusBadGateway)) || strings.Contains(err.Error(), strconv.Itoa(http.StatusServiceUnavailable)) {
				if retryCount >= maxRetry {
					logger.Print(logger.ERROR, "Received 502 error multiple times, returning error", err)
					return
				}
				logger.Print(logger.INFO, "Received 502 Bad Gateway error. Retrying in 5 seconds...")
				time.Sleep(5 * time.Second)
				retryCount++
				continue
			}
			return
		}

		break
	}

	var openAIResponse OpenAIResponse

	if err = json.Unmarshal(resp, &openAIResponse); err != nil {
		logger.Print(logger.ERROR, "Got error calling unmarshal", err)
		return
	}

	if len(openAIResponse.Choices) > 0 {
		respMessage = openAIResponse.Choices[0].Message.Content
	}

	logger.Print(logger.INFO, "OpenAI Response", []string{tenantID}, randomStringForLogging, respMessage)

	return
}

func requestOpenAIChatWithSystemPrompt(reqMessage []OpenAIMessage, topP float64, modelName ...string) (respMessage string, err error) {

	randomStringForLogging := SimpleRandomString(6) // Since this function can be called in multiple threads, need to connect request with response for logging

	logger.Print(logger.INFO, "OpenAI Request", randomStringForLogging, reqMessage)

	var (
		resp                []byte
		rateLimitWaitPeriod = 1
		count               = 1
	)

	model := "gpt-4o-mini"
	if len(modelName) > 0 && len(modelName[0]) > 0 {
		model = modelName[0]
	}

	var openAIRequest = OpenAIRequest{
		Model:    model,
		Messages: reqMessage,
		Top:      topP,
	}

	if len(openAIClient.APIKey) <= 0 {
		err = errors.New("Empty token")
		logger.Print(logger.INFO, "Token invalid", err)
		return
	}

	headers := map[string]string{"Authorization": "Bearer " + openAIClient.APIKey}

	for {

		var buf bytes.Buffer
		if err = json.NewEncoder(&buf).Encode(openAIRequest); err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", err)
			return
		}

		resp, err = transport.SendRequest("POST", "https://api.openai.com/v1/chat/completions", nil, headers, &buf)
		if err != nil {
			if strings.Contains(err.Error(), strconv.Itoa(http.StatusTooManyRequests)) {
				// Rate limit reached
				if count > 3 {
					return

				}
				time.Sleep(time.Duration(rateLimitWaitPeriod) * time.Minute)
				rateLimitWaitPeriod++
				count++
				continue
			}

			return
		}

		break
	}

	var openAIResponse OpenAIResponse

	if err = json.Unmarshal(resp, &openAIResponse); err != nil {
		logger.Print(logger.ERROR, "Got error calling unmarshal", err)
		return
	}

	if len(openAIResponse.Choices) > 0 {
		respMessage = openAIResponse.Choices[0].Message.Content
	}

	logger.Print(logger.INFO, "OpenAI Response", randomStringForLogging, respMessage)

	return
}

// Not being used today
func DoesTextHaveName(text string, insertToCache bool, tenantID string) (hasName bool) {

	textLookupDocID := GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		hasName, _ = doc["hasName"].(bool)
		return
	}

	var textToVerify = text

	if addr, err := ParseAddress(text); err == nil {
		textToVerify = GetEmailNameWithoutSpecialCharacters(addr.Address)
	}

	openAIReqMessage := "'" + textToVerify + "' contains a persons name of any origin, true or false answer only"

	openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID)
	if err != nil {
		// In case ai is not working, assume it is name
		hasName = true
		return
	}

	if strings.Contains(strings.ToLower(openAIRespMessage), "true") || !strings.Contains(strings.ToLower(openAIRespMessage), "false") {
		hasName = true
	}

	if insertToCache {

		if _, err = elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
			Text:       text,
			HasName:    hasName,
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(time.Now().UTC()),
		}, textLookupDocID); err != nil {
			return
		}
	}

	return
}

func DeriveEmailFromText(text, emailFormat string, tenantID string) (email string) {

	// Function not being used today

	textLookupDocID := GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		var ok bool
		if email, ok = doc["email"].(string); ok && len(email) > 0 {
			return
		}
	}

	openAIReqMessage := `Organization has the following email format - ` + emailFormat + `. Derive and output email from ` + text + ` without explanation`

	openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID)
	if err != nil {
		return
	}

	if addr, err := ParseAddress(openAIRespMessage); err == nil {

		email = addr.Address
		emailName := GetEmailNameWithoutSpecialCharacters(email)
		hasName := DoesTextHaveName(emailName, false, tenantID)

		if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
			Text:       text,
			Email:      email,
			HasName:    hasName,
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(time.Now().UTC()),
		}, textLookupDocID); err != nil {
			return
		}

		textEmailLookupDocID := GenerateCombinedHashID(email)

		if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
			Text:       email,
			Email:      email,
			HasName:    hasName,
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(time.Now().UTC()),
		}, textEmailLookupDocID); err != nil {
			return
		}
	}

	return
}

func CheckIfEmailsAreOfUser(emailList []string, tenantID string) (isUserEmail map[string]bool) {

	// Function not being used today

	var remainingEmailList []string
	isUserEmail = make(map[string]bool)

	for _, email := range emailList {

		textLookupDocID := GenerateCombinedHashID(email)

		if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
			isUserEmail[email], _ = doc["hasName"].(bool)
		} else {
			remainingEmailList = append(remainingEmailList, email)
			// default
			isUserEmail[email] = true
		}
	}

	var (
		emailPrefixSet []string
		count          = 0
	)

	for _, email := range remainingEmailList {

		count++
		emailPrefixSet = append(emailPrefixSet, GetEmailNameWithoutSpecialCharacters(email))

		if count > 10 {

			openAIReqMessage := `Output only a key value pair json for the given list of strings where value is true only if the string has a valid persons name. 
			Q. ["jessica_letterman","hello","shivrajgupta","test-kishore","marketplace"] 
			A. {"jessica_letterman":true,"hello":false,"shivrajgupta":true,"test-kishore":true,"marketplace":false} 
			Q. ["` + strings.Join(emailPrefixSet, `","`) + `"]`

			openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID)
			if err != nil {
				return isUserEmail
			}

			var checkNameMap map[string]bool

			if err = json.Unmarshal([]byte(openAIRespMessage), &checkNameMap); err != nil {
				logger.Print(logger.ERROR, "Got error calling unmarshal", err, openAIReqMessage, openAIRespMessage)
				return isUserEmail
			}

			for k, v := range checkNameMap {
				isUserEmail[k] = v
			}

			count = 0
			emailPrefixSet = []string{}
		}
	}

	if count > 0 {

		openAIReqMessage := `Output only a key value pair json for the given list of strings where value is true only if the string has a valid persons name. 
			Q. ["jessica_letterman","hello","shivrajgupta","test-kishore","marketplace"] 
			A. {"jessica_letterman":true,"hello":false,"shivrajgupta":true,"test-kishore":true,"marketplace":false} 
			Q. ["` + strings.Join(emailPrefixSet, `","`) + `"]`

		openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID)
		if err != nil {
			return
		}

		var checkNameMap map[string]bool

		if err = json.Unmarshal([]byte(openAIRespMessage), &checkNameMap); err != nil {
			logger.Print(logger.ERROR, "Got error calling unmarshal", err, openAIReqMessage, openAIRespMessage)
			return
		}

		for k, v := range checkNameMap {
			isUserEmail[k] = v
		}
	}

	for _, email := range remainingEmailList {

		if addr, err := ParseAddress(email); err == nil {

			email = addr.Address

			textLookupDocID := GenerateCombinedHashID(email)

			if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
				Text:       email,
				HasName:    isUserEmail[GetEmailNameWithoutSpecialCharacters(email)],
				Email:      email,
				TenantID:   tenantID,
				InsertTime: elastic.DateTime(time.Now().UTC()),
			}, textLookupDocID); err != nil {
				return
			}
		}
	}

	return
}

func DeriveNamesStringFromDescription(descriptionText, tenantID string) (nameListString string) {

	if descriptionText == "" {
		return ""
	}

	hasName := true

	textLookupDocID := GenerateCombinedHashID(descriptionText + "Derive Names from Description")

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		var ok bool
		if _, ok = doc["hasName"].(bool); ok {
			nameListString, _ = doc["name"].(string)
			return
		}
	}

	openAIReqMessage := `Only return all the human names mentioned in the text in title case. The parts of name should be separated by space and each name should be separated by comma .If there are no human names in the text return null. The text is ` + descriptionText

	nameListString, err := requestOpenAIChat(openAIReqMessage, tenantID)
	if err != nil {
		return
	}

	pattern := `^(null|([A-Za-z]+(?: [A-Za-z]+)?(?: [A-Za-z]+)?(?:, [A-Za-z]+(?: [A-Za-z]+)?(?: [A-Za-z]+)?)*))$`
	regex := regexp.MustCompile(pattern)
	match := regex.MatchString(nameListString)
	if !match || strings.ToLower(nameListString) == "null" {
		nameListString = ""
		hasName = false
	}

	descTextLookupDocID := GenerateCombinedHashID(descriptionText + "Derive Names from Description")

	if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       descriptionText,
		Email:      "",
		Name:       nameListString,
		HasName:    hasName,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Derive Names from Description",
	}, descTextLookupDocID); err != nil {
		return
	}

	return
}

func RetrieveOpenAIApiKey() string {
	return openAIClient.APIKey
}

func DeriveModelDescription(text string, tenantID string) (desc string) {

	textLookupDocID := GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		desc, _ = doc["response"].(string)
		return
	}

	openAIReqMessage := "'" + text + "' This is the model training content. Examine and give a short description of the model. Follow the instructions and provide data authoritatively in below format. {\" Precize Description\" : \"provide description in one paragraph strictly\", \"Use Case\" : \"4-10 words describing the use case\"}"

	desc, err := requestOpenAIChat(openAIReqMessage, tenantID)
	if err != nil {
		return
	}

	if _, err = elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       text,
		Response:   desc,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func ParseTrainingFile(modelType, tenantID string, fileData []byte) (string, error) {
	var result strings.Builder

	switch modelType {
	case "babbage-002", "davinci-002":
		lines := bytes.Split(fileData, []byte("\n"))
		for i, line := range lines {
			if i >= 5 {
				break
			}
			var completion Completion
			if err := json.Unmarshal(line, &completion); err != nil {
				return "", err
			}
			result.WriteString(fmt.Sprintf("Prompt: %s\nCompletion: %s\n\n", completion.Prompt, completion.Completion))
		}
	default:
		lines := bytes.Split(fileData, []byte("\n"))
		for i, line := range lines {
			if i >= 5 {
				break
			}
			var conversation Conversation
			if err := json.Unmarshal(line, &conversation); err != nil {
				return "", err
			}
			for _, msg := range conversation.Messages {
				result.WriteString(fmt.Sprintf("%s: %s\n", msg.Role, msg.Content))
			}
			result.WriteString("\n")
		}
	}

	return result.String(), nil
}

func RequestAIFileSupport(openAIReqText, fileID, tenantID string, fileUploadContent []byte) (openAIResponse MessageResponse, err error) {

	randomStringForLogging := SimpleRandomString(6) // Since this function can be called in multiple threads, need to connect request with response for logging

	logger.Print(logger.INFO, "OpenAI Request for file upload", []string{tenantID}, randomStringForLogging, fileID)

	headers := map[string]string{"Authorization": "Bearer " + openAIClient.APIKey}

	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	err = writer.WriteField("purpose", "fine-tune")
	if err != nil {
		return
	}

	part, err := writer.CreateFormFile("file", fileID+"_"+tenantID+".json")
	if err != nil {
		return
	}
	_, err = io.Copy(part, bytes.NewReader(fileUploadContent))
	if err != nil {
		return
	}
	err = writer.Close()
	if err != nil {
		return
	}

	headers["Content-Type"] = writer.FormDataContentType()

	fileResp, err := transport.SendRequest("POST", baseURL+"/files", nil, headers, &requestBody)
	if err != nil {
		return
	}

	var fileResponse OpenAIFile

	if err = json.Unmarshal(fileResp, &fileResponse); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
		return
	}

	messages := []MessageReq{
		{
			Role:    "user",
			Content: openAIReqText,
			Attachments: []Attachment{
				{
					FileID: fileResponse.ID,
					Tools: []Tool{
						{Type: "file_search"},
					},
				},
			},
		},
	}

	headers = make(map[string]string)

	// Create the thread request
	threadRequest := ThreadRequest{
		AssistantID: "asst_4ANrAP30Bh5dITM4HlBfNPbq",
		Thread: Thread{
			Messages: messages,
		},
	}

	var createAssistantBuff bytes.Buffer

	if err = json.NewEncoder(&createAssistantBuff).Encode(threadRequest); err != nil {
		logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
		return
	}

	headers["OpenAI-Beta"] = "assistants=v2"
	headers["Authorization"] = "Bearer " + openAIClient.APIKey

	resp, err := transport.SendRequest("POST", baseURL+"/threads/runs", nil, headers, &createAssistantBuff)
	if err != nil {
		return
	}

	var threadResponse ThreadResponse

	if err = json.Unmarshal(resp, &threadResponse); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
		return
	}

	//TODO: convert to a stream api instead of waiting
	time.Sleep(1 * time.Minute)

	headers["OpenAI-Beta"] = "assistants=v2"

	queryParams := make(map[string]string)
	queryParams = map[string]string{
		"limit": "1",
	}

	resp, err = transport.SendRequest("GET", baseURL+"/threads/"+threadResponse.ThreadID+"/messages", queryParams, headers, nil)
	if err != nil {
		return
	}

	var messagesResp MessageResponse

	if err = json.Unmarshal(resp, &messagesResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{tenantID}, err)
		return
	}

	_, err = transport.SendRequest("DELETE", baseURL+"/threads/"+threadResponse.ThreadID, nil, headers, nil)
	if err != nil {
		return
	}

	_, err = transport.SendRequest("DELETE", baseURL+"/files/"+fileResponse.ID, nil, headers, nil)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "OpenAI Response for file upload", []string{tenantID}, randomStringForLogging, messagesResp)

	return messagesResp, nil
}

func DeriveModelTrainingFileTag(text string, tenantID string) (desc string) {

	textLookupDocID := GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		desc, _ = doc["response"].(string)
		return
	}

	openAIReqMessage := "'" + text + "' This is the model training content. Answer whether it contains any Payment Card Industry (pci),Protected Health Information (Phi), Personally Identifiable Information (Pii) information. Follow the instructions and provide data authoritatively in below format. {\"HasPii\" : \"bool value\", \"HasPci\" : \"bool value\", \"HasPhi\" : \"bool value\"}"

	desc, err := requestOpenAIChat(openAIReqMessage, tenantID)
	if err != nil {
		return
	}

	if _, err = elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       text,
		Response:   desc,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func DeriveSensitivityFromText(text, tenantID string, openAIReqMessage []OpenAIMessage) (sensitivityListString string) {

	if text == "" {
		return ""
	}

	hasSensitivity := true

	textLookupDocID := GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		var ok bool
		if _, ok = doc["hasName"].(bool); ok {
			sensitivityListString, _ = doc["name"].(string)
			return
		}
	}

	sensitivityListString, err := requestOpenAIChatWithSystemPrompt(openAIReqMessage, 0.7, "")
	if err != nil {
		return
	}

	if strings.ToLower(sensitivityListString) == "null" {
		sensitivityListString = ""
		hasSensitivity = false
	}
	descTextLookupDocID := GenerateCombinedHashID(text)

	if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       text,
		Email:      "",
		Name:       sensitivityListString,
		HasName:    hasSensitivity,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Derive Sensitivity from text",
	}, descTextLookupDocID); err != nil {
		return
	}

	return
}

func DeriveJiraContextFromText(text, tenantID string, openAIReqMessage []OpenAIMessage) (jiraRespString string) {

	if text == "" {
		return ""
	}

	hasSensitivity := true

	textLookupDocID := GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		var ok bool
		if _, ok = doc["hasName"].(bool); ok {
			jiraRespString, _ = doc["name"].(string)
			return
		}
	}

	jiraRespString, err := requestOpenAIChatWithSystemPrompt(openAIReqMessage, 0.1, "gpt-4o-mini")
	if err != nil {
		return
	}

	if strings.ToLower(jiraRespString) == "null" {
		jiraRespString = ""
		hasSensitivity = false
	}

	if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       text,
		Email:      "",
		Name:       jiraRespString,
		HasName:    hasSensitivity,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Jira Ticket Context",
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func HumanOrNonHumanName(inputNames map[string][]string, exampleNames map[string]bool, tenantID string) map[string]bool {
	resp := make(map[string]bool)
	batchSize := 100

	aiInput := make([]string, 0, len(inputNames))
	for inputName := range inputNames {

		textLookupDocID := GenerateCombinedHashID(inputName)

		if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
			hasName, _ := doc["hasName"].(bool)
			insertDate, _ := doc["insertTime"].(string)

			layout := time.RFC3339
			insertedDate, err := elastic.ParseDateTime(insertDate)
			if err != nil {
				inputName = "\"" + inputName + "\""
				aiInput = append(aiInput, inputName)
			} else {

				thresholdTime, _ := time.Parse(layout, "2025-01-20T00:00:01.063Z")

				if insertedDate.Before(thresholdTime) {
					inputName = "\"" + inputName + "\""
					aiInput = append(aiInput, inputName)
				} else {
					resp[inputName] = hasName
				}
			}
		} else {
			inputName = "\"" + inputName + "\""
			aiInput = append(aiInput, inputName)
		}
	}

	// Process in batches
	for i := 0; i < len(aiInput); i += batchSize {
		end := i + batchSize
		if end > len(aiInput) {
			end = len(aiInput)
		}
		currentBatch := aiInput[i:end]

		// Construct the prompt with few-shot examples
		openAIReqMessage := `Given a list of text strings, analyze each string to determine if it contains a person's name by following these rules:
		1. Consider both Western and non-Western name formats.
		2. Exclude common words that might look like names but are actually:
		- Company names
		- Product names
		- Common nouns that happen to match name patterns.
		
		Return a JSON object where each input string is a key, and its corresponding value is a boolean (true if it contains a human name, false otherwise). 

		STRICTLY follow this format:
		{
		"name1": true,
		"name2": false,
		...
		}

		Ensure that the values are proper boolean types (not strings). Do NOT use quotes around 'true' or 'false'.

		Examples:`

		for name, isHumanName := range exampleNames {
			openAIReqMessage += fmt.Sprintf("\n- %s: %v", name, isHumanName)
		}

		openAIReqMessage += "\n\nInputs: " + strings.Join(currentBatch, "\n")

		openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID, "gpt-4o-mini")
		if err != nil {
			// default ai response if ai returns error
			for _, name := range currentBatch {
				name = strings.Trim(name, "\"")
				resp[name] = true
			}
			continue
		}

		var parsedResponse map[string]bool
		if err := json.Unmarshal([]byte(openAIRespMessage), &parsedResponse); err != nil {

			// openai returns ```json in response sometimes which fails the parsing
			if strings.Contains(openAIRespMessage, "```json") {
				openAIRespMessage = strings.ReplaceAll(openAIRespMessage, "```json", "")
				openAIRespMessage = strings.ReplaceAll(openAIRespMessage, "```", "")
				openAIRespMessage = strings.TrimSpace(openAIRespMessage)

				if err := json.Unmarshal([]byte(openAIRespMessage), &parsedResponse); err != nil {
					logger.Print(logger.ERROR, "Failed to unmarshall openAI response", []string{tenantID}, openAIRespMessage, err)
					continue
				}
			} else {
				logger.Print(logger.ERROR, "Failed to unmarshall openAI response", []string{tenantID}, openAIRespMessage, err)
				continue
			}
		}

		// if a name is not present in the response, it is assumed to be a human name to avoid recursive calls
		for _, name := range currentBatch {
			name = strings.Trim(name, "\"")
			if _, ok := parsedResponse[name]; !ok {
				parsedResponse[name] = true
			}
		}

		bulkTextLookUpRequest := ""
		for text, hasName := range parsedResponse {

			// insert for true -> false only for now.
			if _, ok := resp[text]; !ok {
				textLookupDocID := GenerateCombinedHashID(text)
				resp[text] = hasName
				doc := TextLookupInsertDoc{
					Text:       text,
					HasName:    hasName,
					TenantID:   tenantID,
					InsertTime: elastic.DateTime(time.Now().UTC()),
				}

				textLookUpMetadata := `{"index": {"_id": "` + textLookupDocID + `"}}`
				textLookUpInsertDoc, err := json.Marshal(doc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling document", err)
					continue
				}

				bulkTextLookUpRequest += textLookUpMetadata + "\n" + string(textLookUpInsertDoc) + "\n"
			}
		}

		// Send bulk request for this batch
		if len(bulkTextLookUpRequest) > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.TEXT_LOOKUP_INDEX, bulkTextLookUpRequest); err != nil {
				continue
			}
		}
	}

	return resp
}

func IsSoftwareComponent(input string, exampleInput map[string]bool, tenantID string) (resp bool) {

	lookUpStr := strings.Trim(input, "\"")
	textLookupDocID := GenerateCombinedHashID(lookUpStr)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		hasName, _ := doc["hasName"].(bool)
		resp = hasName
		return
	} else {

		openAIReqMessage := `Determine if the following refers to a software component. A software component is a reusable, modular part of a software system that performs a specific function and is typically integrated into other applications or systems. It is *not* a full product, platform, or end-user application. Respond only with true or false (no quotes, no explanation).

		Examples:
		Input: redis  
		Output: true

		Input: precize 
		Output: false

		Input: jenkins 
		Output: true

		Input: myserver
		Output: false

		Input: azdo  
		Output: false

		Now classify the following:
		Input: ` + input + `
		Output:`

		openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID, "gpt-4o-mini")
		if err != nil {
			// default ai response if ai returns error
			resp = false
			return
		}

		if strings.TrimSpace(strings.ReplaceAll(openAIRespMessage, `"`, ``)) == "true" {
			resp = true
		}
	}

	if _, err := elastic.InsertDocument(tenantID, elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       input,
		HasName:    resp,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Software Component",
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func DeriveCategoryForIncident(openAIReqMessage, tenantID string) (categoryRespString string) {

	if openAIReqMessage == "" {
		return ""
	}

	isValidResp := true

	textLookupDocID := GenerateCombinedHashID(openAIReqMessage)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		var ok bool
		if _, ok = doc["hasName"].(bool); ok {
			categoryRespString, _ = doc["name"].(string)
			return
		}
	}

	categoryRespString, err := requestOpenAIChat(openAIReqMessage, tenantID, "gpt-4o-mini")
	if err != nil {
		return
	}

	if strings.ToLower(categoryRespString) == "null" {
		categoryRespString = ""
		isValidResp = false
	}

	if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       openAIReqMessage,
		HasName:    isValidResp,
		Name:       categoryRespString,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Incident Category",
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func DeriveIncidentIntroDescFromCategories(categories, tenantID string, openAIReqMessage []OpenAIMessage) (descRespString string) {

	if categories == "" {
		return ""
	}

	hasDesc := true

	textLookupDocID := GenerateCombinedHashID(categories)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		var ok bool
		if _, ok = doc["hasName"].(bool); ok {
			descRespString, _ = doc["name"].(string)
			return
		}
	}

	descRespString, err := requestOpenAIChatWithSystemPrompt(openAIReqMessage, 0.1, "gpt-4o-mini")
	if err != nil {
		return
	}

	if strings.ToLower(descRespString) == "null" {
		descRespString = ""
		hasDesc = false
	}
	if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       categories,
		Name:       descRespString,
		HasName:    hasDesc,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Incident Intro Description",
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func IsValidOwnerAttribution(resourceName, derivedOwner string, tenantID string, excludeEvaluation bool) (isValid bool) {

	if excludeEvaluation {
		return true
	}

	if strings.Contains(derivedOwner, EX_EMPLOYEE_PREFIX) {
		derivedOwner = strings.TrimPrefix(derivedOwner, EX_EMPLOYEE_PREFIX)
	}

	if addr, err := mail.ParseAddress(derivedOwner); err == nil {
		derivedOwner = addr.Name
	}

	if len(derivedOwner) == 0 || len(resourceName) == 0 {
		return false
	}

	attributionLookupID := GenerateCombinedHashID(resourceName + ":" + derivedOwner)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, attributionLookupID); len(doc) > 0 {
		isValid, _ = doc["hasName"].(bool)
		return
	}

	openAIReqMessage := fmt.Sprintf(`Resource name: "%s"
	Derived potential owner: "%s"
	Determine whether the resource name is explicitly named after this person (human), using either their full name or a clear and unique portion of their name (e.g., first name or last name) as a naming convention. Ignore partial or coincidental matches and do not consider substrings or common terms.
	Only answer "Yes" if the resource name is very likely a direct reference to the person's name. If there is any doubt or if the name could be generic, descriptive, or unrelated to the person, answer "No". 
	
	IMPORTANT RULES:
	- Do NOT match if the resource name refers to well-known applications, services, tools, or technologies (e.g., jenkins, maria, docker, redis, nginx, apache, postgres, etc.)

	Answer only Yes or No.
	
	Examples:
		Input: Resource name: john's-test-bucket; Derived potential owner: John
		Output: Yes

		Input: Resource name: provider-instance; Derived potential owner: Provider
		Output: No

		Input: Resource name: jenkins-prod-resource; Derived potential owner: John Jenkins
		Output: No

		Input: Resource name: maria-database; Derived potential owner: Maria Rodriguez
		Output: No

	`, resourceName, derivedOwner)

	openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID)
	if err != nil {
		// In case AI is not working
		isValid = true
		return
	}

	responseLower := strings.ToLower(openAIRespMessage)
	if strings.Contains(responseLower, "yes") && !strings.Contains(responseLower, "no") {
		isValid = true
	}

	if _, err = elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       resourceName + ":" + derivedOwner,
		HasName:    isValid,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Resource Name Owner Derivation",
	}, attributionLookupID); err != nil {
		return
	}

	return
}

func DeriveSensitivityFromInstanceStartupScript(script, tenantID string) (resp bool) {

	textLookupDocID := GenerateCombinedHashID(script)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		hasName, _ := doc["hasName"].(bool)
		resp = hasName
		return
	}

	openAIReqMessage := fmt.Sprintf(`Does the following Instance startup script contain any private SSH key or password, using which the machine can be accessed from internet or be breached (Note: public ssh key should not be considered as sensitive). Strictly follow the instructions. Answer only in True or False.\n\n%s`, script)

	openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID, "gpt-4o-mini")
	if err != nil {
		// default ai response if ai returns error
		resp = false
		return
	}

	if strings.Contains(strings.ToLower(openAIRespMessage), "true") {
		resp = true
	}

	if _, err := elastic.InsertDocument(tenantID, elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       script,
		HasName:    resp,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Derive Sensitivity from Script",
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func SeparateHumanNameFromNameString(nameString, tenantID string) (formattedName string) {

	if nameString == "" {
		return ""
	}

	regex := regexp.MustCompile(`[^a-zA-Z0-9]`)
	nameParts := regex.Split(nameString, -1)

	if len(nameParts) > 1 {
		return ""
	}

	hasName := true
	textLookupDocID := GenerateCombinedHashID(strings.ToLower(nameString) + "Formatted Human Name")

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		var ok bool
		if _, ok = doc["hasName"].(bool); ok {
			formattedName, _ = doc["name"].(string)
			return
		}
	}

	openAIReqMessage := fmt.Sprintf(`Extract a human name from string. Consider both Western and non-Western names. Remove numeric or irrelevant parts. Return the name only in "First Middle Last" format (if middle exists) separated by space. If no valid name is found, return none. Strictly follow the instructions.

		Examples:
		"johndoe" -> "John Doe"
		"john_doe_personal" -> "John Doe"
		"jane.m.smith123" -> "Jane M Smith"
		"admin_system" → ""	

		Input: "%s"`, nameString)

	formattedNameResp, err := requestOpenAIChat(openAIReqMessage, tenantID)
	if err != nil {
		return
	}

	if strings.Contains(strings.ToLower(formattedNameResp), "none") {
		hasName = false
		formattedNameResp = ""
	}

	descTextLookupDocID := GenerateCombinedHashID(strings.ToLower(nameString) + "Formatted Human Name")

	if _, err := elastic.InsertDocument("Global", elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       strings.ToLower(nameString),
		Email:      "",
		Name:       formattedNameResp,
		HasName:    hasName,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Formatted Human Name",
	}, descTextLookupDocID); err != nil {
		return
	}

	return
}

func GenerateAssistantDescriptionFromNameandInstructions(assistantName, assistantInstructions, tenantID string) (openAIRespMessage string, err error) {
	if assistantName == "" && assistantInstructions == "" {
		return
	}

	textLookupDocID := GenerateCombinedHashID(assistantName + ":" + assistantInstructions)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		openAIRespMessage, _ = doc["response"].(string)
		return
	}

	openAIReqMessage := fmt.Sprintf(`Generate a short description (mostly describe the purpose of the assistant) for the following assistant name and instructions. The assistant name is "%s" and the instructions are "%s". The description should be in 2 lines only. Strictly return a description string only do not return json`, assistantName, assistantInstructions)

	openAIRespMessage, err = requestOpenAIChat(openAIReqMessage, tenantID, "gpt-4o-mini")
	if err != nil || openAIRespMessage == "" {
		return
	}

	if _, err = elastic.InsertDocument(tenantID, elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       assistantName + ":" + assistantInstructions,
		Response:   openAIRespMessage,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Assistant Description",
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func DeriveSensitivityFromRawText(text, tenantID string) (sensitivityTags string, err error) {

	textLookupDocID := GenerateCombinedHashID(text + "Derive Sensitivity from Raw Text")

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		sensitivityTags, _ = doc["response"].(string)
		return
	}

	openAIReqMessage := fmt.Sprintf(`Does the following text contain or will likely contain any Payment Card Industry (pci),Protected Health Information (Phi), Personally Identifiable Information (Pii) information? Follow the instructions, provide data in below format only and do not give any extra description. Return true only if u are certain. {"HasPii" : "boolean value", "HasPci" : "boolean value", "HasPhi" : "boolean value", "Description" : "which data lead to sensitivity"}\n\n%s`, text)

	sensitivityTags, err = requestOpenAIChat(openAIReqMessage, tenantID, "gpt-4o-mini")
	if err != nil {
		// default ai response if ai returns error
		sensitivityTags = ""
		return
	}

	if _, err = elastic.InsertDocument(tenantID, elastic.TEXT_LOOKUP_INDEX, TextLookupInsertDoc{
		Text:       text,
		Response:   sensitivityTags,
		TenantID:   tenantID,
		InsertTime: elastic.DateTime(time.Now().UTC()),
		Category:   "Derive Sensitivity from Raw Text",
	}, textLookupDocID); err != nil {
		return
	}

	return
}

func TeamNamesFromList(resourceNames []string, tenantID string) (teams []string) {

	var (
		teamPrefix = "team:::"
		batchSize  = 100
		aiInput    = make([]string, 0, len(resourceNames))
	)

	for _, resourceName := range resourceNames {

		team := resourceName
		text := teamPrefix + resourceName
		textLookupDocID := GenerateCombinedHashID(text)

		if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
			if hasName, _ := doc["hasName"].(bool); hasName {
				teams = append(teams, team)
			}
		} else {
			aiInput = append(aiInput, resourceName)
		}
	}

	// Process in batches
	for i := 0; i < len(aiInput); i += batchSize {
		end := i + batchSize
		if end > len(aiInput) {
			end = len(aiInput)
		}
		currentBatch := aiInput[i:end]

		openAIReqMessage := `For an organization, identify which of the following parent resource names could **definitely** be the name of a corporate team. A team name typically reflects a function, internal tool, client-facing unit, product group, or location-based team (e.g., "devops_in", "zeus", "unilever", "nokia"). Do **not** include personal names, generic terms like "project", or temporary/test names.

		Return only those inputs that clearly represent a team name based on common enterprise naming conventions.
		Output must be a **JSON array** containing only the valid team names from the input. Do not return anything else.

		### Example
		Input: ["infra-management", "my-project", "tetris", "jessica", "devops_in", "unilever", "tech-12345"]
		Output: ["infra-management", "tetris", "devops_in", "unilever"]

		### Input: ["` + strings.Join(currentBatch, `","`) + `"]`

		openAIRespMessage, err := requestOpenAIChat(openAIReqMessage, tenantID, "gpt-4o-mini")
		if err != nil {
			// default ai response if ai returns error
			continue
		}

		var parsedResponse []string
		if err := json.Unmarshal([]byte(openAIRespMessage), &parsedResponse); err != nil {
			// openai returns ```json in response sometimes which fails the parsing
			if strings.Contains(openAIRespMessage, "```json") {
				openAIRespMessage = strings.ReplaceAll(openAIRespMessage, "```json", "")
				openAIRespMessage = strings.ReplaceAll(openAIRespMessage, "```", "")
				openAIRespMessage = strings.TrimSpace(openAIRespMessage)

				if err := json.Unmarshal([]byte(openAIRespMessage), &parsedResponse); err != nil {
					logger.Print(logger.ERROR, "Failed to unmarshall openAI response", []string{tenantID}, openAIRespMessage, err)
					continue
				}
			} else {
				logger.Print(logger.ERROR, "Failed to unmarshall openAI response", []string{tenantID}, openAIRespMessage, err)
				continue
			}
		}

		bulkTextLookUpRequest := ""
		for _, resourceName := range currentBatch {

			var (
				hasName bool
				text    = teamPrefix + resourceName
			)

			if slices.Contains(parsedResponse, resourceName) {
				hasName = true
				teams = append(teams, resourceName)
			}

			textLookupDocID := GenerateCombinedHashID(text)
			doc := TextLookupInsertDoc{
				Text:       text,
				HasName:    hasName,
				TenantID:   tenantID,
				InsertTime: elastic.DateTime(time.Now().UTC()),
			}

			textLookUpMetadata := `{"index": {"_id": "` + textLookupDocID + `"}}`
			textLookUpInsertDoc, err := json.Marshal(doc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				continue
			}

			bulkTextLookUpRequest += textLookUpMetadata + "\n" + string(textLookUpInsertDoc) + "\n"
		}

		// Send bulk request for this batch
		if len(bulkTextLookUpRequest) > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.TEXT_LOOKUP_INDEX, bulkTextLookUpRequest); err != nil {
				continue
			}
		}
	}

	return
}
