package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"

	"github.com/precize/logger"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

func parseTfFunctions(name string, parameters interface{}) (interface{}, error) {
	switch name {
	case "abs":
		return parseAbs(parameters)
	case "ceil":
		return parseCeil(parameters)
	case "floor":
		return parseFloor(parameters)
	case "max":
		return parseMax(parameters)
	case "min":
		return parseMin(parameters)
	case "pow":
		return parsePow(parameters)
	case "lower":
		return parseLower(parameters)
	case "upper":
		return parseUpper(parameters)
	case "join":
		var arr []interface{}
		if arr, ok := parameters.([]interface{}); ok {
			return arr, nil
		}
		return parseJoinElements(arr[0].(string), arr[1:])
	case "title":
		return parseTitle(parameters)
	case "sum":
		return parseSum(parameters)
	case "length":
		return parseLength(parameters)
	case "jsondecode":
		return parseJsonDecode(parameters)
	case "jsonencode":
		return parseJsonEncode(parameters)
	default:
		logger.Print(logger.INFO, "Unsupported function name", name)
		return nil, nil
	}
}

func parseAbs(parameters interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameters.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse abs function", parameters)
		return nil, errors.New("Unable to parse abs function")
	}

	if len(arr) < 2 {
		logger.Print(logger.ERROR, "min function requires at least two parameters", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 1 {
		logger.Print(logger.ERROR, "abs function requires exactly one parameter", parameters)
		return nil, errors.New("error")
	}

	if value, isFloat := arr[0].(float64); isFloat {
		return math.Abs(value), nil
	} else if value, isInt := arr[0].(int); isInt {
		return math.Abs(float64(value)), nil
	}

	logger.Print(logger.ERROR, "abs function requires a numeric parameter", parameters)
	return nil, errors.New("error")
}

func parseCeil(parameters interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameters.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse ceil function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 1 {
		logger.Print(logger.ERROR, "ceil function requires exactly one parameter", parameters)
		return nil, errors.New("error")
	}

	if value, ok := arr[0].(float64); ok {
		result := math.Ceil(value)
		return result, nil
	}

	logger.Print(logger.ERROR, "ceil function requires a numeric parameter", parameters)
	return nil, errors.New("error")
}

func parseFloor(parameters interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameters.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse floor function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 1 {
		logger.Print(logger.ERROR, "Floor function requires exactly one parameter", parameters)
		return nil, errors.New("error")
	}
	if value, ok := arr[0].(float64); ok {
		result := math.Floor(value)
		return result, nil
	}
	logger.Print(logger.ERROR, "Floor function requires a numeric parameter", parameters)
	return nil, errors.New("error")
}

func parseMin(parameter interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameter.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse min function", parameter)
		return nil, errors.New("error")
	}

	if len(arr) < 2 {
		logger.Print(logger.ERROR, "Min function requires at least two parameters", parameter)
		return nil, errors.New("error")
	}

	var convertedParams []float64
	for _, param := range arr {
		if value, isFloat := param.(float64); isFloat {
			convertedParams = append(convertedParams, value)
		} else if value, isInt := param.(int64); isInt {
			convertedParams = append(convertedParams, float64(value))
		} else {
			logger.Print(logger.ERROR, "Min function requires numeric parameters", parameter)
			return nil, errors.New("error")
		}
	}

	result := math.Min(convertedParams[0], convertedParams[1])
	for _, param := range convertedParams[2:] {
		result = math.Min(result, param)
	}

	return result, nil
}

func parseMax(parameters interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameters.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse max function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) < 2 {
		logger.Print(logger.ERROR, "Max function requires at least two parameters", parameters)
		return nil, errors.New("error")
	}

	var convertedParams []float64
	for _, param := range arr {
		if value, isFloat := param.(float64); isFloat {
			convertedParams = append(convertedParams, value)
		} else if value, isInt := param.(int64); isInt {
			convertedParams = append(convertedParams, float64(value))
		} else {
			logger.Print(logger.ERROR, "Max function requires numeric parameters", parameters)
			return nil, errors.New("error")
		}
	}

	result := math.Max(convertedParams[0], convertedParams[1])
	for _, param := range convertedParams[2:] {
		result = math.Max(result, param)
	}

	return result, nil
}

func parsePow(parameters interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameters.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse pow function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 2 {
		logger.Print(logger.ERROR, "Pow function requires exactly two parameters", parameters)
		return nil, errors.New("error")
	}

	base, baseIsFloat := arr[0].(float64)
	exponent, exponentIsFloat := arr[1].(float64)

	if !baseIsFloat {
		base = float64(arr[0].(int))
	}

	if !exponentIsFloat {
		exponent = float64(arr[1].(int))
	}

	result := math.Pow(base, exponent)
	return result, nil
}

func parseJoinElements(separator string, elements interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = elements.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse join function", separator, elements)
		return nil, errors.New("error")
	}

	var stringElements []string

	for _, element := range arr {
		stringValue := fmt.Sprintf("%v", element)
		stringElements = append(stringElements, stringValue)
	}

	return strings.Join(stringElements, separator), nil
}

func parseLower(input interface{}) (string, error) {
	stringValue := fmt.Sprintf("%v", input)
	return strings.ToLower(stringValue), nil
}

func parseUpper(input interface{}) (string, error) {
	stringValue := fmt.Sprintf("%v", input)
	return strings.ToUpper(stringValue), nil
}

func parseTitle(input interface{}) (string, error) {
	stringValue := fmt.Sprintf("%v", input)
	caser := cases.Title(language.English)
	titlecased := caser.String(stringValue)

	return titlecased, nil
}

func parseSum(parameters interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameters.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse sum function", parameters)
		return nil, errors.New("error")
	}

	var sumResult float64
	for _, param := range arr {

		if value, isInt := param.(int64); isInt {
			sumResult += float64(value)
		} else if value, isInt := param.(float64); isInt {
			sumResult += value
		}
	}

	return sumResult, nil
}

func parseLength(input interface{}) (int, error) {
	switch v := input.(type) {
	case string:
		return len(v), nil
	case []interface{}:
		return len(v), nil
	default:
		logger.Print(logger.ERROR, "Unsupported type for length function", input)
		return 0, errors.New("error")
	}
}

func parseJsonDecode(parameters interface{}) (interface{}, error) {
	var arr []interface{}
	var ok bool
	if arr, ok = parameters.([]interface{}); !ok {
		logger.Print(logger.ERROR, "Unable to parse pow function", parameters)
		return nil, errors.New("error")
	}
	stringValue := fmt.Sprintf("%v", arr[0])

	var singleValue interface{}

	err := json.Unmarshal([]byte(stringValue), &singleValue)
	if err == nil {
		return singleValue, nil
	}

	var mapValue map[string]string
	err = json.Unmarshal([]byte(stringValue), &mapValue)
	if err == nil {
		return mapValue, nil
	}
	logger.Print(logger.ERROR, "Unable to parse jsondecode function", parameters)
	return nil, errors.New("error")
}

func parseJsonEncode(parameter interface{}) (interface{}, error) {

	jsonBytes, err := json.Marshal(parameter)
	if err != nil {
		logger.Print(logger.ERROR, "Got Error while unmarshalling parseJsonEncode func", err)
		return "", err
	}

	jsonString := string(jsonBytes)
	return jsonString, nil

}
