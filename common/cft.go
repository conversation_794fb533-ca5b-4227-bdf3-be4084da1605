package common

import (
	"encoding/json"
	"fmt"

	"github.com/precize/logger"
)

var (
	priorityProperties = map[string]struct{}{
		"UserData": {},
	}
)

func fetchLeafNodes(data map[string]interface{}, result *interface{}) {
	for _, v := range data {
		switch val := v.(type) {
		case map[string]interface{}:
			fetchLeafNodes(val, result)
		case string:
			if *result == nil {
				*result = val
			} else {
				valStr := (*result).(string) + val
				*result = valStr
			}
		default:
			switch v := (*result).(type) {
			case []interface{}:
				v = append(v, val)
				*result = v
			default:
				vArr := make([]interface{}, 0)
				vArr = append(vArr, val)
				*result = vArr
			}

		}
	}
}

func ProcessCftTemplate(fileContentJson map[string]interface{}, tenantID string) (string, error) {

	resources, ok := fileContentJson["Resources"].(map[string]interface{})
	if !ok {
		logger.Print(logger.INFO, "Resources filed not present in template")
		return "", fmt.Errorf("Resources filed not present in template")
	}

	priorityConfigs := make(map[string]interface{})

	for resourceName, resource := range resources {
		properties, ok := resource.(map[string]interface{})["Properties"].(map[string]interface{})
		if !ok {
			continue
		}

		for propName, propValue := range properties {

			if propValue, ok := propValue.(map[string]interface{}); ok {

				if _, ok := priorityProperties[propName]; ok {
					var value interface{}
					fetchLeafNodes(propValue, &value)
					if value != nil {
						priorityConfigs[resourceName] = map[string]interface{}{
							propName: value,
						}
					}
				}
			}
		}
	}

	if len(priorityConfigs) > 0 {
		priorityConfigsJson, err := json.Marshal(priorityConfigs)
		if err != nil {
			logger.Print(logger.ERROR, "Erros Marshalling commitIdsUpdatedVars")
			return "", err
		}
		priorityConfigsJsonStr := string(priorityConfigsJson)
		return priorityConfigsJsonStr, nil
	}

	return "", nil
}
