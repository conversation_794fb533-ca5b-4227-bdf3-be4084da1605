package common

type DefenderRecommendationResp struct {
	TotalRecords    int                       `json:"totalRecords"`
	Count           int                       `json:"count"`
	Data            []DefenderRecommendations `json:"data"`
	ResultTruncated string                    `json:"resultTruncated"`
	SkipToken       string                    `json:"$skipToken"`
}

type DefenderRecommendations struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	Type           string `json:"type"`
	ResourceGroup  string `json:"resourceGroup"`
	TenantID       string `json:"tenantId"`
	SubscriptionID string `json:"subscriptionId"`
	Properties     struct {
		ResourceDetails struct {
			ResourceName     string `json:"ResourceName"`
			ResourceType     string `json:"ResourceType"`
			NativeResourceID string `json:"NativeResourceId"`
			ResourceID       string `json:"ResourceId"`
			Source           string `json:"Source"`
			ID               string `json:"Id"`
		} `json:"resourceDetails"`
		DisplayName string `json:"displayName"`
		Status      struct {
			Code                string `json:"code"`
			FirstEvaluationDate string `json:"firstEvaluationDate"`
		} `json:"status"`
		AdditionalData struct {
			SubAssessmentsLink string `json:"subAssessmentsLink"`
		} `json:"additionalData"`
		Metadata struct {
			Severity               string   `json:"severity"`
			Description            string   `json:"description"`
			AssessmentType         string   `json:"assessmentType"`
			RemediationDescription string   `json:"remediationDescription"`
			PolicyDefinitionID     string   `json:"policyDefinitionId"`
			Categories             []string `json:"categories"`
			UserImpact             string   `json:"userImpact"`
			Threats                []string `json:"threats"`
			ImplementationEffort   string   `json:"implementationEffort"`
		} `json:"metadata"`
		Risk struct {
			RiskFactors []string `json:"riskFactors"`
			Level       string   `json:"level"`
		} `json:"risk"`
	} `json:"properties"`
	StatusChangeDate string `json:"statusChangeDate"`
}

type DefenderRecommendationFindingsResp struct {
	Data []DefenderRecFindings `json:"data"`
}

type DefenderRecFindings struct {
	ID         string `json:"id"`
	Properties struct {
		VulnID      string `json:"id"`
		DisplayName string `json:"displayName"`
		Description string `json:"description"`
		Impact      string `json:"impact"`
		Status      struct {
			Code     string `json:"code"`
			Severity string `json:"severity"`
		} `json:"status"`
		AdditionalData struct {
			Score float32 `json:"cvssV30Score"`
		} `json:"additionalData"`
	} `json:"properties"`
}

type DefenderAlertsSubscriptionsResp struct {
	SkipToken string                        `json:"$skipToken"`
	Data      []DefenderAlertsSubscriptions `json:"data"`
}

type DefenderAlertsSubscriptions struct {
	SubscriptionID string `json:"subscriptionId"`
}

type DefenderAlertsResp struct {
	Value    []DefenderAlert `json:"value"`
	NextLink string          `json:"nextLink"`
}

type DefenderAlert struct {
	ID         string                  `json:"id"`
	Properties DefenderAlertProperties `json:"properties"`
}

type DefenderAlertProperties struct {
	Status              string                             `json:"status"`
	TimeGeneratedUTC    string                             `json:"timeGeneratedUtc"`
	Severity            string                             `json:"severity"`
	Intent              string                             `json:"intent"`
	ResourceIdentifiers []DefenderAlertResourceIdentifiers `json:"resourceIdentifiers"`
	AlertDisplayName    string                             `json:"alertDisplayName"`
	Description         string                             `json:"description"`
	ExtendedProperties  map[string]interface{}             `json:"extendedProperties"`
}

type DefenderAlertResourceIdentifiers struct {
	AzureResourceID       string `json:"azureResourceId"`
	Type                  string `json:"type"`
	AzureResourceTenantID string `json:"azureResourceTenantId"`
}
