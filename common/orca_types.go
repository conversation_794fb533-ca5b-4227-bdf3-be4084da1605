package common

var (
	OrcaToEntityTypeMappings = map[string]string{
		"CloudAccount":         "Project",
		"GcpCertificate":       "compute.googleapis.com/SslCertificate",
		"GcpIamServiceAccount": "ServiceAccount",
		"GcpLoadBalancer":      "LoadBalancer",
		"GcpUser":              "GCPUser",
		"K8sDeployment":        "apps.k8s.io/Deployment",
		"K8sStatefulSet":       "k8s.io/Namespace",
		"containerimage":       "ContainerImage",
		"vm":                   "VMInstance",
		"gke":                  "GKECluster",
		"GcpVmDisk":            "VMDisks",
	}
)

type Data struct {
	MitreCategory      string   `json:"mitre_category"`
	Recommendation     string   `json:"recommendation"`
	Details            string   `json:"details"`
	MitreTechniques    []string `json:"mitre_techniques"`
	Title              string   `json:"title"`
	RemediationConsole []string `json:"remediation_console"`
}

type State struct {
	Severity           string      `json:"severity"`
	RuleSource         string      `json:"rule_source"`
	LastUpdated        string      `json:"last_updated"`
	LastSeen           string      `json:"last_seen"`
	InVerification     bool        `json:"in_verification"`
	LowSince           string      `json:"low_since"`
	CreatedAt          string      `json:"created_at"`
	Score              int         `json:"score"`
	RiskLevel          string      `json:"risk_level"`
	OrcaScore          interface{} `json:"orca_score"`
	AlertID            string      `json:"alert_id"`
	HighSince          string      `json:"high_since"`
	StatusTime         string      `json:"status_time"`
	Status             string      `json:"status"`
	ClosedReason       string      `json:"closed_reason"`
	VerificationStatus string      `json:"verification_status"`
}

type OrcaAlert struct {
	Data                   Data     `json:"data"`
	IsCompliance           bool     `json:"is_compliance"`
	RelatedCompliances     []string `json:"related_compliances"`
	Recommendation         string   `json:"recommendation"`
	Source                 string   `json:"source"`
	GroupType              string   `json:"group_type"`
	ClusterType            string   `json:"cluster_type"`
	Type                   string   `json:"type"`
	Details                string   `json:"details"`
	State                  State    `json:"state"`
	RuleQuery              string   `json:"rule_query"`
	CloudVendorID          string   `json:"cloud_vendor_id"`
	AssetState             string   `json:"asset_state"`
	OrganizationID         string   `json:"organization_id"`
	AssetUniqueID          string   `json:"asset_unique_id"`
	CloudAccountType       string   `json:"cloud_account_type"`
	AlertLabels            []string `json:"alert_labels"`
	Configuration          struct{} `json:"configuration"`
	Description            string   `json:"description"`
	CloudAccountID         string   `json:"cloud_account_id"`
	AccountName            string   `json:"account_name"`
	Category               string   `json:"category"`
	AssetName              string   `json:"asset_name"`
	AssetType              string   `json:"asset_type"`
	AlertAvailabilityZones []string `json:"asset_availability_zones"`
	Model                  Model    `json:"model"`
	Findings               Findings `json:"findings"`
}

type Findings struct {
	CVE []CVEDetails `json:"cve"`
}

type CVEDetails struct {
	ID          string      `json:"cve_id"`
	Description string      `json:"summary"`
	Impact      string      `json:"impact"`
	Score       interface{} `json:"cvss3_score"`
	Severity    string      `json:"severity"`
}

type Model struct {
	Data ModelData `json:"data"`
}

type ModelData struct {
	Inventory Inventory `json:"Inventory"`
}

type Inventory struct {
	IsInternetFacing bool `json:"IsInternetFacing"`
}

type OrcaAdditionalInfo struct {
	IsInternetFacing       bool                     `json:"isInternetFacing"`
	RecommendationFindings []RecommendationFindings `json:"recommendationFindings"`
}

type OrcaAssetData struct {
	Compute         Compute `json:"compute"`
	AssetName       string  `json:"asset_name"`
	AssetType       string  `json:"asset_type"`
	AssetTypeString string  `json:"asset_type_string"`
	AccountName     string  `json:"account_name"`
	Model           struct {
		Data struct {
			Content       ModelContent  `json:"Content"`
			Compute       ModelCompute  `json:"Compute"`
			GcpVmInstance GcpVmInstance `json:"GcpVmInstance"`
		} `json:"data"`
	} `json:"model"`
}

type Compute struct {
	DistributionName    string   `json:"distribution_name"`
	NumVcpusApi         int      `json:"num_vcpus_api"`
	EsmStatus           string   `json:"esm_status"`
	MemoryApi           int      `json:"memory_api"`
	Hostname            string   `json:"hostname"`
	DistributionVersion string   `json:"distribution_version"`
	PrivateIps          []string `json:"private_ips"`
	TotalDisksBytes     int64    `json:"total_disks_bytes"`
	PublicIps           []string `json:"public_ips"`
	Disks               []Disk   `json:"disks"`
}

type Disk struct {
	Size string `json:"size"`
	Used string `json:"used"`
}

type ModelContent struct {
	HasSensitiveKeys             bool     `json:"HasSensitiveKeys"`
	PiiTypes                     []string `json:"PiiTypes"`
	HasMalwareWithHighConfidence bool     `json:"HasMalwareWithHighConfidence"`
	Name                         string   `json:"Name"`
	HasPii                       bool     `json:"HasPii"`
}

type ModelCompute struct {
	Applications     Applications `json:"Applications"`
	OsEndOfSupport   string       `json:"OsEndOfSupport"`
	PublicIps        []string     `json:"PublicIps"`
	CpuType          string       `json:"CpuType"`
	DistMajorVersion string       `json:"DistributionMajorVersion"`
}

type Applications struct {
	Models    []ApplicationModel `json:"models"`
	Remaining int                `json:"remaining"`
}

type ApplicationModel struct {
	ModelItem ModelItem `json:"model"`
}

type ModelItem struct {
	Name string `json:"name"`
	ID   string `json:"id"`
	Type string `json:"type"`
}

type GcpVmInstance struct {
	CpuPlatform                string `json:"CpuPlatform"`
	UpdateAutoLearnPolicy      bool   `json:"UpdateAutoLearnPolicy"`
	EnableSecureBoot           bool   `json:"EnableSecureBoot"`
	MachineType                string `json:"MachineType"`
	DeletionProtection         bool   `json:"DeletionProtection"`
	ConfidentialComputeEnabled bool   `json:"ConfidentialComputeEnabled"`
}
