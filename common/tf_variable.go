package common

import (
	"encoding/json"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/google/go-github/v48/github"
	"github.com/precize/logger"
	"github.com/precize/transport"
	"github.com/xanzy/go-gitlab"
)

func fetchTerraformVariableFiles(provider string, gitClient interface{}, variableFiles *[]VariableFile) error {
	switch provider {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			err := fetchVariableFilesGithub(filepath.Dir(githubApiClient.FilePath), *githubApiClient, variableFiles)
			if err != nil {
				return err
			}
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			err := fetchVariableFilesBitbucket(*bitbucketApiClient, variableFiles, "commit_directory", filepath.Dir(bitbucketApiClient.FilePath))
			if err != nil {
				return err
			}
		}

	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			err := fetchVariableFilesGitlab(*gitlabApiClient, variableFiles)
			if err != nil {
				return err
			}
		}
	default:
		logger.Print(logger.INFO, "Provider not supported", provider)
	}
	return nil
}

func fetchVariableFilesGithub(path string, githubApiClient GithubApiClient, variablesFileContents *[]VariableFile) error {
	repoContent, directories, _, err := githubApiClient.GithubClient.Repositories.GetContents(githubApiClient.Context, githubApiClient.RepoOwner, githubApiClient.RepoName, path, &github.RepositoryContentGetOptions{
		Ref: githubApiClient.CommitSHA,
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error fetching directories from path for github", []string{githubApiClient.TenantID, githubApiClient.RepoName, path, githubApiClient.CommitSHA}, err)
		return err
	}

	if repoContent != nil {
		contentString, err := repoContent.GetContent()
		if err != nil {
			logger.Print(logger.ERROR, "Got error fetching directory tree for github", []string{githubApiClient.TenantID, githubApiClient.RepoName}, err)
			return err
		}
		if contentString != "" && (strings.HasSuffix(path, "variables.tf") || strings.HasSuffix(path, "var.tf") || strings.HasSuffix(path, ".tfvars") || strings.HasSuffix(path, ".tfvars.json")) {
			*variablesFileContents = append(*variablesFileContents, VariableFile{
				fileName: *repoContent.Name,
				content:  contentString,
			})
		}
	}

	for _, directory := range directories {
		fetchVariableFilesGithub((*directory.Path), githubApiClient, variablesFileContents)
	}
	return nil
}

func fetchVariableFilesGitlab(gitlabApiClient GitlabApiClient, variablesFileContents *[]VariableFile) error {
	var (
		recursive         = true
		filePath          = ""
		err               error
		directoryContents []*gitlab.TreeNode
		dirResp           *gitlab.Response
		directoryPage     = 1
	)

	gitlabClient := refreshGitlabToken(gitlabApiClient.AccessToken)
	for {
		if gitlabApiClient.Branch == "<unknown>" {
			directoryContents, dirResp, err = gitlabClient.Repositories.ListTree(gitlabApiClient.ProjectID, &gitlab.ListTreeOptions{
				Path:      &filePath,
				Recursive: &recursive,
				ListOptions: gitlab.ListOptions{
					Page:    directoryPage,
					PerPage: 90,
				},
			})
			if err != nil {
				if strings.Contains(err.Error(), "401") {
					gitlabClient = refreshGitlabToken(gitlabApiClient.AccessToken)
					directoryContents, dirResp, err = gitlabClient.Repositories.ListTree(gitlabApiClient.ProjectID, &gitlab.ListTreeOptions{
						Path:      &filePath,
						Recursive: &recursive,
						ListOptions: gitlab.ListOptions{
							Page:    directoryPage,
							PerPage: 90,
						},
					})
					if err != nil {
						logger.Print(logger.ERROR, "Got error getting directory tree for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID)}, err)
						return err
					}
				} else {
					logger.Print(logger.ERROR, "Got error fetching directory tree for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID), filePath}, err)
					return err
				}
			}
		} else {
			directoryContents, dirResp, err = gitlabClient.Repositories.ListTree(gitlabApiClient.ProjectID, &gitlab.ListTreeOptions{
				Path:      &filePath,
				Ref:       &gitlabApiClient.Branch,
				Recursive: &recursive,
			})
			if err != nil {
				if strings.Contains(err.Error(), "401") {
					gitlabClient = refreshGitlabToken(gitlabApiClient.AccessToken)
					directoryContents, dirResp, err = gitlabClient.Repositories.ListTree(gitlabApiClient.ProjectID, &gitlab.ListTreeOptions{
						Path:      &filePath,
						Ref:       &gitlabApiClient.Branch,
						Recursive: &recursive,
					})
					if err != nil {
						logger.Print(logger.ERROR, "Got error getting directory tree for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID)}, err)
						return err
					}
				} else {
					logger.Print(logger.ERROR, "Got error fetching directory tree for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID), filePath}, err)
					return err
				}
			}
		}

		for _, fileContent := range directoryContents {
			if fileContent.Type == "blob" && (strings.HasSuffix(fileContent.Path, "variables.tf") || strings.HasSuffix(fileContent.Path, "var.tf") || strings.HasSuffix(fileContent.Path, ".tfvars") || strings.HasSuffix(fileContent.Path, ".tfvars.json")) {
				contentString, _, err := gitlabApiClient.GitlabClient.RepositoryFiles.GetRawFile(gitlabApiClient.ProjectID, fileContent.Path, &gitlab.GetRawFileOptions{Ref: gitlab.String(gitlabApiClient.CommitID)})
				if err != nil {
					if strings.Contains(err.Error(), "401") {
						gitlabClient := refreshGitlabToken(gitlabApiClient.AccessToken)
						contentString, _, err = gitlabClient.RepositoryFiles.GetRawFile(gitlabApiClient.ProjectID, fileContent.Path, &gitlab.GetRawFileOptions{Ref: gitlab.String(gitlabApiClient.CommitID)})
						if err != nil {
							logger.Print(logger.ERROR, "Got error getting commit raw content", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID)}, err)
							return err
						}
					} else {
						logger.Print(logger.ERROR, "Got error fetching raw file for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID), fileContent.Path}, err)
						continue
					}
				}
				*variablesFileContents = append(*variablesFileContents, VariableFile{
					fileName: fileContent.Name,
					content:  string(contentString),
				})
			}
		}

		if dirResp.NextPage == 0 {
			break
		}

		directoryPage = dirResp.NextPage
	}
	return nil
}

func fetchVariableFilesBitbucket(bitbucketApiClient BitbucketApiClient, variablesFileContents *[]VariableFile, fileType string, filePath string) error {
	fileContentUrl := bitbucketApiClient.Domain + "/2.0/repositories/" + bitbucketApiClient.WorkspaceSlug + "/" +
		bitbucketApiClient.RepoSlug + "/src/" + bitbucketApiClient.CommitHash + "/" + filePath

	fileContentResp, err := transport.SendRequest(
		"GET",
		fileContentUrl,
		nil,
		map[string]string{"Authorization": "Bearer " + bitbucketApiClient.Token},
		nil,
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling while fetching directory contents for bitbucket", []string{bitbucketApiClient.WorkspaceSlug, bitbucketApiClient.RepoSlug, bitbucketApiClient.CommitHash, filePath}, err)
		return err
	}

	if fileType == "commit_file" {
		if strings.HasSuffix(filePath, "variables.tf") || strings.HasSuffix(filePath, "var.tf") || strings.HasSuffix(filePath, ".tfvars") || strings.HasSuffix(filePath, ".tfvars.json") {
			*variablesFileContents = append(*variablesFileContents, VariableFile{
				fileName: filePath,
				content:  string(fileContentResp),
			})
		}
	} else if fileType == "commit_directory" {
		var dirContents BitBucketDirectoryContents
		if err = json.Unmarshal(fileContentResp, &dirContents); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling while fetching directory contents for bitbucket", err)
			return err
		}
		for _, dirContent := range dirContents.Values {
			fetchVariableFilesBitbucket(bitbucketApiClient, variablesFileContents, dirContent.Type, dirContent.Path)
		}
	}
	return nil
}
