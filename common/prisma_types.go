package common

import "time"

type PrismaAlert struct {
	AlertAdditionalInfo map[string]interface{}   `json:"alertAdditionalInfo"`
	AlertAttribution    AlertAttribution         `json:"alertAttribution"`
	AlertCount          int                      `json:"alertCount"`
	AlertRules          []AlertRule              `json:"alertRules"`
	AlertTime           int64                    `json:"alertTime"`
	AppMetadata         []map[string]interface{} `json:"appMetadata"`
	EventOccurred       int64                    `json:"eventOccurred"`
	FirstSeen           int64                    `json:"firstSeen"`
	ID                  string                   `json:"id"`
	LastSeen            int64                    `json:"lastSeen"`
	LastUpdated         int64                    `json:"lastUpdated"`
	Metadata            map[string]interface{}   `json:"metadata"`
	Policy              PrismaPolicy             `json:"policy"`
	PolicyID            string                   `json:"policyId"`
	Reason              string                   `json:"reason"`
	Resource            PrismaResource           `json:"resource"`
	RiskDetail          PrismaRiskDetail         `json:"riskDetail"`
	Status              string                   `json:"status"`
	TriggeredBy         string                   `json:"triggeredBy"`
	// ConnectionDetails  []ConnectionDetail      `json:"connectionDetails"`
	// DismissalDuration  string                  `json:"dismissalDuration"`
	// DismissalNote      string                  `json:"dismissalNote"`
	// DismissalUntilTs   int64                   `json:"dismissalUntilTs"`
	// DismissedBy        string                  `json:"dismissedBy"`
	// History            []History               `json:"history"`
	// InvestigateOptions InvestigateOptions      `json:"investigateOptions"`
	// SaveSearchID       string                  `json:"saveSearchId"`
}

type AlertAttribution struct {
	AttributionEventList []AttributionEvent `json:"attributionEventList"`
	ResourceCreatedBy    string             `json:"resourceCreatedBy"`
	ResourceCreatedOn    int64              `json:"resourceCreatedOn"`
}

type AttributionEvent struct {
	Event    string `json:"event"`
	EventTs  int64  `json:"event_ts"`
	Username string `json:"username"`
}

type AlertRule struct {
	Name               string   `json:"name"`
	Enabled            bool     `json:"enabled"`
	Description        string   `json:"description"`
	AllowAutoRemediate bool     `json:"allowAutoRemediate"`
	NotifyOnDismissed  bool     `json:"notifyOnDismissed"`
	NotifyOnOpen       bool     `json:"notifyOnOpen"`
	NotifyOnResolved   bool     `json:"notifyOnResolved"`
	NotifyOnSnoozed    bool     `json:"notifyOnSnoozed"`
	Policies           []string `json:"policies"`
	// AlertRuleNotificationConfig []AlertRuleNotificationConfig `json:"alertRuleNotificationConfig"`
}

// type AlertRuleNotificationConfig struct {
// 	DayOfMonth   int      `json:"dayOfMonth"`
// 	DaysOfWeek   []DayOfWeek `json:"daysOfWeek"`
// 	Enabled      bool     `json:"enabled"`
// 	Frequency    string   `json:"frequency"`
// 	HourOfDay    int      `json:"hourOfDay"`
// 	Recipients   []string `json:"recipients"`
// 	Type         string   `json:"type"`
// 	WithCompression bool  `json:"withCompression"`
// }

// type DayOfWeek struct {
// 	Day    string `json:"day"`
// 	Offset int    `json:"offset"`
// }

// type ConnectionDetail struct {
// 	Accepted              string                 `json:"accepted"`
// 	AccountName           string                 `json:"accountName"`
// 	Classification        string                 `json:"classification"`
// 	DestIP                string                 `json:"destIp"`
// 	DestISP               string                 `json:"destIsp"`
// 	FeedSource            string                 `json:"feedSource"`
// 	ID                    int                    `json:"id"`
// 	InboundTrafficVolume  int                    `json:"inboundTrafficVolume"`
// 	OutboundTrafficVolume int                    `json:"outboundTrafficVolume"`
// 	Packets               int                    `json:"packets"`
// 	SrcIP                 string                 `json:"srcIp"`
// 	SrcISP                string                 `json:"srcIsp"`
// 	ThreatDescription     string                 `json:"threatDescription"`
// 	Timestamp             int64                  `json:"timestamp"`
// }

// type History struct {
// 	Reason string `json:"reason"`
// 	Status string `json:"status"`
// }

// type InvestigateOptions struct {
// 	AlertID                 string `json:"alertId"`
// 	HasSearchExecutionSupport bool   `json:"hasSearchExecutionSupport"`
// 	SearchID                string `json:"searchId"`
// }

type PrismaPolicy struct {
	CloudType   string `json:"cloudType"`
	Description string `json:"description"`
	Enabled     bool   `json:"enabled"`
	Name        string `json:"name"`
	Severity    string `json:"severity"`
}

type PrismaResource struct {
	Account          string                 `json:"account"`
	AccountID        string                 `json:"accountId"`
	AdditionalInfo   map[string]interface{} `json:"additionalInfo"`
	CloudServiceName string                 `json:"cloudServiceName"`
	CloudType        string                 `json:"cloudType"`
	ID               string                 `json:"id"`
	Name             string                 `json:"name"`
	Region           string                 `json:"region"`
	ResourceApiName  string                 `json:"resourceApiName"`
	ResourceTags     map[string]interface{} `json:"resourceTags"`
	ResourceType     string                 `json:"resourceType"`
}

type PrismaRiskDetail struct {
	RiskScore PrismaRiskScore `json:"riskScore"`
}

type PrismaRiskScore struct {
	MaxScore int `json:"maxScore"`
	Score    int `json:"score"`
}

type RuntimeHostAudit struct {
	ID                string            `json:"_id"`
	AccountID         string            `json:"accountID"`
	App               string            `json:"app"`
	AppID             string            `json:"appID"`
	AttackTechniques  []string          `json:"attackTechniques"`
	AttackType        []string          `json:"attackType"`
	Cluster           string            `json:"cluster"`
	Collections       []string          `json:"collections"`
	Command           string            `json:"command"`
	Container         bool              `json:"container"`
	ContainerID       string            `json:"containerId"`
	ContainerName     string            `json:"containerName"`
	Count             int               `json:"count"`
	Country           string            `json:"country"`
	Domain            string            `json:"domain"`
	Effect            []string          `json:"effect"`
	Err               string            `json:"err"`
	Filepath          string            `json:"filepath"`
	FQDN              string            `json:"fqdn"`
	Function          string            `json:"function"`
	FunctionID        string            `json:"functionID"`
	Hostname          string            `json:"hostname"`
	ImageID           string            `json:"imageId"`
	ImageName         string            `json:"imageName"`
	Interactive       bool              `json:"interactive"`
	IP                string            `json:"ip"`
	Label             string            `json:"label"`
	Labels            map[string]string `json:"labels"`
	MD5               string            `json:"md5"`
	Msg               string            `json:"msg"`
	Namespace         string            `json:"namespace"`
	OS                string            `json:"os"`
	PID               int               `json:"pid"`
	Port              int               `json:"port"`
	ProcessPath       string            `json:"processPath"`
	ProfileID         string            `json:"profileId"`
	Provider          []string          `json:"provider"`
	RawEvent          string            `json:"rawEvent"`
	Region            string            `json:"region"`
	RequestID         string            `json:"requestID"`
	ResourceID        string            `json:"resourceID"`
	RuleName          string            `json:"ruleName"`
	Runtime           []string          `json:"runtime"`
	Severity          []string          `json:"severity"`
	Time              time.Time         `json:"time"`
	Type              []string          `json:"type"`
	User              string            `json:"user"`
	Version           string            `json:"version"`
	VMID              string            `json:"vmID"`
	WildFireReportURL string            `json:"wildFireReportURL"`
}
