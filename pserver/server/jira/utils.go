package jira

import (
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
)

const (
	JIRA_DATA_CENTER_REQUEST_TYPE = "Data Center"
	JIRA_CLOUD_REQUEST_TYPE       = "Cloud"
)

func updateStatusOfExistingPrecizeTickets(baseReq jiraBaseRequest, herostat string, serviceIDs serviceIDMap, requestType string) error {

	var (
		issues, closedIssues []string
	)

	aggregateIssueKeysFromIncidentsQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + baseReq.TenantID + `"}},{"term":{"heroStatDbKey.keyword":"` + herostat + `"}},{"terms":{"serviceId":[` + strings.Join(serviceIDs.Include, `,`) + `]}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}]}},"from":0,"size":0,"aggs":{"ticketAggregation":{"terms":{"field":"pmTicket.keyword","size":10000,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}`

	if len(serviceIDs.Exclude) > 0 {
		aggregateIssueKeysFromIncidentsQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + baseReq.TenantID + `"}},{"term":{"heroStatDbKey.keyword":"` + herostat + `"}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}],"must_not":[{"terms":{"serviceId":[` + strings.Join(serviceIDs.Exclude, `,`) + `]}}]}},"from":0,"size":0,"aggs":{"ticketAggregation":{"terms":{"field":"pmTicket.keyword","size":10000,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}`
	}

	incidentAggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_INCIDENTS_INDEX}, aggregateIssueKeysFromIncidentsQuery)
	if err != nil {
		return err
	}

	if ownerAggs, ok := incidentAggsResp["ticketAggregation"].(map[string]interface{}); ok {
		if ticketBuckets, ok := ownerAggs["buckets"].([]interface{}); ok {
			for _, ticketBucket := range ticketBuckets {
				if ticketBucketMap, ok := ticketBucket.(map[string]interface{}); ok {
					if issueKey, ok := ticketBucketMap["key"].(string); ok {
						issues = append(issues, issueKey)
					}
				}
			}
		}
	}

	if len(issues) <= 0 {
		return nil
	}

	if strings.Contains(requestType, JIRA_CLOUD_REQUEST_TYPE) {
		closedIssues, err = getJiraCloudClosedIssuesList(baseReq, issues)
		if err != nil {
			return err
		}
	} else {
		closedIssues, err = getJiraDataCenterClosedIssuesList(baseReq, issues)
		if err != nil {
			return err
		}
	}

	if len(closedIssues) > 0 {

		removeClosedTicketsQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + baseReq.TenantID + `"}},{"terms":{"pmTicket.keyword":["` + strings.Join(closedIssues, `","`) + `"]}},{"term":{"heroStatDbKey.keyword":"` + herostat + `"}},{"terms":{"serviceId":[` + strings.Join(serviceIDs.Include, `,`) + `]}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}]}},"script":"ctx._source.pmTicket = null;"}`

		if len(serviceIDs.Exclude) > 0 {
			removeClosedTicketsQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + baseReq.TenantID + `"}},{"terms":{"pmTicket.keyword":["` + strings.Join(closedIssues, `","`) + `"]}},{"term":{"heroStatDbKey.keyword":"` + herostat + `"}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}],"must_not":[{"terms":{"serviceId":[` + strings.Join(serviceIDs.Exclude, `,`) + `]}}]}},"script":"ctx._source.pmTicket = null;"}`
		}

		if err = elastic.UpdateByQuery(elastic.CLOUD_INCIDENTS_INDEX, removeClosedTicketsQuery); err != nil {
			return err
		}

		// Wait for it to update elastic. Same is being fetched post this call.
		time.Sleep(2 * time.Second)
	}

	return nil
}
