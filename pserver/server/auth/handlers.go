package auth

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/logger"
	"github.com/precize/provider/bitbucket"
	"github.com/precize/provider/github"
	"github.com/precize/provider/gitlab"
	"github.com/precize/provider/jira"
	"github.com/precize/provider/okta"
	"github.com/precize/provider/openai"
	"github.com/precize/provider/orca"
	"github.com/precize/provider/prismacloud"
	"github.com/precize/provider/tenant"
	"github.com/precize/provider/wiz"
)

type validateRequest struct {
	TenantID string      `json:"tenantId"`
	Service  string      `json:"service"`
	Auth     interface{} `json:"auth"`
}

type pserverAuthResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func Validate(w http.ResponseWriter, r *http.Request) {

	logger.Print(logger.INFO, "Request received to validate auth")

	req, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request", err)
		json.NewEncoder(w).Encode(pserverAuthResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var reqBody validateRequest

	if err = json.Unmarshal(req, &reqBody); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", err)
		json.NewEncoder(w).Encode(pserverAuthResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	if len(reqBody.Service) <= 0 || len(reqBody.TenantID) <= 0 {
		json.NewEncoder(w).Encode(pserverAuthResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Missing required parameters"})
		logger.Print(logger.INFO, "Response sent", "Missing required parameters")
		return
	}

	var (
		authValidation        = make(map[string]bool)
		parseError, authError error
		b                     []byte
	)

	switch reqBody.Service {
	case common.GITHUB_SERVICE_CODE:

		var providerEnv tenant.GithubEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = github.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.GITLAB_SERVICE_CODE:

		var providerEnv tenant.GitlabEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = gitlab.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.BITBUCKET_SERVICE_CODE:

		var providerEnv tenant.BitbucketEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = bitbucket.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.JIRA_SERVICE_CODE:

		var providerEnv tenant.JiraEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = jira.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.OKTA_SERVICE_CODE:

		var providerEnv tenant.OktaEnvironment

		logger.Print(logger.INFO, "Received", reqBody.Auth)

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				logger.Print(logger.INFO, "Received 1 - ", string(b))
				authValidation, authError = okta.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.ORCA_SERVICE_CODE:

		var providerEnv tenant.OrcaEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = orca.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.OPENAI_SERVICE_CODE:

		var providerEnv tenant.OpenAIEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = openai.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.PRISMA_SERVICE_CODE:

		var providerEnv tenant.PrismaCloudEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = prismacloud.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	case common.WIZ_SERVICE_CODE:

		var providerEnv tenant.WizEnvironment

		if b, parseError = json.Marshal(reqBody.Auth); parseError == nil {
			if parseError = json.Unmarshal(b, &providerEnv); parseError == nil {
				authValidation, authError = wiz.ValidateAuth(providerEnv, reqBody.TenantID)
			}
		}

	default:
		logger.Print(logger.ERROR, "Unknown service")
		json.NewEncoder(w).Encode(pserverAuthResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Unknown service"})
		logger.Print(logger.INFO, "Response sent", "Unknown service")
		return
	}

	if parseError != nil {
		logger.Print(logger.ERROR, parseError.Error())
		json.NewEncoder(w).Encode(pserverAuthResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Failed to parse"})
		logger.Print(logger.INFO, "Response sent", "Failed to parse")
		return
	}

	if authError != nil {
		errorMessage := ""
		if !strings.Contains(authError.Error(), "Authorization pending") {
			errorMessage += "Invalid auth - "
		}
		json.NewEncoder(w).Encode(pserverAuthResponse{Status: strconv.Itoa(http.StatusOK), Message: errorMessage + authError.Error(),
			Data: authValidation})
		logger.Print(logger.INFO, "Response sent", authValidation)
		return
	}

	json.NewEncoder(w).Encode(pserverAuthResponse{Status: strconv.Itoa(http.StatusOK), Message: "Authentication valid",
		Data: authValidation})
	logger.Print(logger.INFO, "Response sent", "Authentication valid")
	return
}
